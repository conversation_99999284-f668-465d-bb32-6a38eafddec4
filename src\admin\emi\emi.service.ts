// Imports
import { v4 as uuidv4 } from 'uuid';
import {
  k422ErrorMessage,
  kInternalError,
  kInvalidParamValue,
  kParamMissing,
} from 'src/constants/responses';
import * as fs from 'fs';
import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { EmiEntity } from 'src/entities/emi.entity';
import { IncludeOptions, Op, Sequelize } from 'sequelize';
import { k500Error } from 'src/constants/misc';
import { registeredUsers } from 'src/entities/user.entity';
import { EMIRepository } from 'src/repositories/emi.repository';
import { SharedNotificationService } from 'src/shared/services/notification.service';
import { AllsmsService } from 'src/thirdParty/SMS/sms.service';
import { CryptService } from 'src/utils/crypt.service';
import { TypeService } from 'src/utils/type.service';
import { WhatsAppService } from 'src/thirdParty/whatsApp/whatsApp.service';
import { RedisService } from 'src/redis/redis.service';
import {
  BIFURCATION_DAYS,
  ECS_BOUNCE_CHARGE,
  GLOBAL_CHARGES,
  MSG91,
  PAGE_LIMIT,
  SYSTEM_ADMIN_ID,
  UAT_PHONE_NUMBER,
  gIsPROD,
} from 'src/constants/globals';
import {
  kAutoDebit,
  kCollectionEmail,
  kCompleted,
  kEMIPay,
  kFailed,
  kFullPay,
  kInfoBrandNameNBFC,
  kHelpContact,
  kInitiated,
  kSupportMail,
  kTOverDue,
  nbfcInfoStr,
  kNoReplyMail,
  kLspNoReplyMail,
  klspCollectionMail,
  kTechSupportMail,
  kDirectBankPay,
  kDelay5Days,
  kChangeFollowerId,
  kfinvu,
  kCAMS,
  kCapActive,
} from 'src/constants/strings';
import { kParamsMissing } from 'src/constants/responses';
import { TransactionEntity } from 'src/entities/transaction.entity';
import { BankingEntity } from 'src/entities/banking.entity';
import { disbursementEntity } from 'src/entities/disbursement.entity';
import { SubScriptionEntity } from 'src/entities/subscription.entity';
import { loanTransaction } from 'src/entities/loan.entity';
import { UserRepository } from 'src/repositories/user.repository';
import { LoanRepository } from 'src/repositories/loan.repository';
import { nPaymentRedirect } from 'src/constants/network';
import { DateService } from 'src/utils/date.service';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { FileService } from 'src/utils/file.service';
import { CommonService } from 'src/utils/common.service';
import { PredictionEntity } from 'src/entities/prediction.entity';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { BankingService } from 'src/admin/banking/banking.service';
import { InsuranceEntity } from 'src/entities/insurance.entity';
import { StringService } from 'src/utils/string.service';
import { TransactionRepository } from 'src/repositories/transaction.repository';
import { SequelOptions } from 'src/interfaces/include.options';
import { CalculationSharedService } from 'src/shared/calculation.service';
import { BlockUserHistoryRepository } from 'src/repositories/user.blockHistory.repository';
import {
  kEmailPaymentReminder,
  kMismatchedEmiCreation,
} from 'src/constants/directories';
import { SystemTraceEntity } from 'src/entities/system_trace.entity';
import { BankingRepository } from 'src/repositories/banking.repository';
import { FinvuService } from 'src/thirdParty/finvu/finvu.service';
import { EnvConfig } from 'src/configs/env.config';
import { CibilScoreEntity } from 'src/entities/cibil.score.entity';
import {
  kDummyUserIds,
  kLspMsg91Templates,
  kMsg91Templates,
} from 'src/constants/objects';
import { CallingService } from '../calling/calling.service';
import { AALogs } from 'src/entities/schemas/aa_logs_schema';
import { GoogleCompanyResultEntity } from 'src/entities/company.entity';
import { SlackService } from 'src/thirdParty/slack/slack.service';
import { ErrorContextService } from 'src/utils/error.context.service';
import { MasterEntity } from 'src/entities/master.entity';
import { BankingSharedService } from 'src/shared/banking.service';
import { LegalCollectionRepository } from 'src/repositories/legal.collection.repository';
import { ChangeLogsRepository } from 'src/repositories/changeLogs.repository';
import { ElephantService } from 'src/thirdParty/elephant/elephant.service';
import { ReportAnalysisService } from '../report/analysis/report.analysis.service';
import { CamsServiceThirdParty } from 'src/thirdParty/cams/cams.service.thirdParty';
import { DefaulterService } from '../defaulter/defaulter.service';

@Injectable()
export class EMIService {
  constructor(
    private readonly dateService: DateService,
    private readonly repository: EMIRepository,
    private readonly repoManager: RepositoryManager,
    private readonly typeService: TypeService,
    private readonly sharedNotification: SharedNotificationService,
    private readonly allSmsService: AllsmsService,
    private readonly whatsAppService: WhatsAppService,
    private readonly cryptService: CryptService,
    private readonly userRepo: UserRepository,
    private readonly loanRepo: LoanRepository,
    private readonly emiRepo: EMIRepository,
    private readonly callingService: CallingService,
    private readonly redisService: RedisService,
    // Admin services
    @Inject(forwardRef(() => BankingService))
    private readonly bankingService: BankingService,
    // Repositories
    private readonly transRepo: TransactionRepository,
    private readonly userBlockHistoryRepo: BlockUserHistoryRepository,
    private readonly bankingRepo: BankingRepository,
    // Shared services
    @Inject(forwardRef(() => CalculationSharedService))
    private readonly calculation: CalculationSharedService,
    private readonly commonService: CommonService,
    private readonly commonsharedService: CommonSharedService,
    private readonly fileService: FileService,
    // Utils services
    private readonly stringService: StringService,
    // third Party services
    @Inject(forwardRef(() => FinvuService))
    private readonly finvuService: FinvuService,
    private readonly slackService: SlackService,
    private readonly errorContextService: ErrorContextService,
    @Inject(forwardRef(() => BankingSharedService))
    private readonly bankingSharedService: BankingSharedService,
    private readonly legalRepo: LegalCollectionRepository,
    private readonly changeLogsRepo: ChangeLogsRepository,
    private readonly elephantService: ElephantService,
    private readonly reportAnalysisService: ReportAnalysisService,
    @Inject(forwardRef(() => CamsServiceThirdParty))
    private readonly camsService: CamsServiceThirdParty,
    @Inject(forwardRef(() => DefaulterService))
    private readonly defaulterService: DefaulterService,
  ) {}

  //notify user upcoming user and due emis
  async funNotifyUpcomingAndDueEmis() {
    try {
      //take current date and future 5 days
      let futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 5);
      futureDate = this.typeService.getGlobalDate(futureDate);
      const userInclude = {
        model: registeredUsers,
        attributes: [
          'id',
          'fullName',
          'fcmToken',
          'phone',
          'email',
          'hashPhone',
        ],
      };
      const attributes = [
        'id',
        'payment_due_status',
        'payment_status',
        'userId',
        'emi_date',
        'emi_amount',
        'penalty',
        'loanId',
        'principalCovered',
        'interestCalculate',
        'paid_principal',
        'paid_interest',
        'paid_penalty',
        'paidBounceCharge',
        'paidPenalCharge',
        'paidLegalCharge',
        'paidRegInterestAmount',
        'regInterestAmount',
        'bounceCharge',
        'gstOnBounceCharge',
        'dpdAmount',
        'penaltyChargesGST',
        'legalCharge',
        'legalChargeGST',
      ];
      const loanInclude = {
        model: loanTransaction,
        attributes: ['appType', 'penaltyCharges'],
      };
      const options = {
        where: {
          emi_date: { [Op.lte]: futureDate.toJSON() },
          payment_status: '0',
          payment_due_status: { [Op.ne]: '1' },
        },
        include: [userInclude, loanInclude],
      };
      //get all upcoming and due emis
      const emiData = await this.repository.getTableWhereData(
        attributes,
        options,
      );
      if (emiData == k500Error) return kInternalError;
      emiData.forEach((emi) => {
        if (!emi?.loan?.penaltyCharges?.MODIFICATION_CALCULATION) {
          emi.bounceCharge = 0;
        }
      });
      await this.sendUpcomingNotifyEmis(emiData);
      return emiData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async getDataForRepaymentStatus(reqData) {
    const adminId = reqData.adminId;
    const maskOptions: any = await this.commonsharedService.findMaskRole(
      adminId,
    );
    const countOnly = reqData?.isCount == 'true';
    if (countOnly) return await this.getCountsForRepaymentStatus(reqData);
    let fromDate = reqData.fromDate;
    let toDate = reqData.endDate;
    if (!fromDate)
      fromDate = this.typeService.getGlobalDate(new Date()).toJSON();
    if (!toDate) toDate = this.typeService.getGlobalDate(new Date()).toJSON();
    const type = reqData.type;
    if (!type) return kParamMissing('type');
    let searchText = (reqData.searchText ?? '').toLowerCase();
    if (searchText <= 2) searchText = '';
    const needDownload = reqData.download == 'true';
    const screenShotBase = parseInt(reqData.screenShotBase ?? 0);
    if (isNaN(screenShotBase) || screenShotBase < 0 || screenShotBase > 2)
      return kInvalidParamValue('screenShotBase');

    // DB query
    const attributes: any = [
      'id',
      'emi_amount',
      'emi_date',
      'emiNumber',
      'loanId',
      'payment_done_date',
      'payment_status',
      'pay_type',
      'userId',
      'principalCovered',
      'interestCalculate',
      'adScreenshotUrl',
    ];
    const limit = reqData.pageSize ?? PAGE_LIMIT;
    const page = reqData.page ?? 1;
    const offset = limit * (page - 1);
    const download = reqData.download === 'true';
    const isRefresh = reqData?.isRefresh == 'true';

    const phoneMask = maskOptions?.isMaskPhone ? 'masked' : 'unmasked';
    const accMask = maskOptions?.isDisbursementAccMask ? 'masked' : 'unmasked';

    const redisKey = `REPAYMENT_STATUS_${adminId}_${type}_${page}_${fromDate}_${toDate}_download-${download}_phone-${phoneMask}_acc-${accMask}`;
    const CACHE_TTL = 60 * 60;

    const cachedData = await this.redisService.get(redisKey);
    if (cachedData && !isRefresh) {
      const parsed = JSON.parse(cachedData);
      return {
        ...parsed,
        lastUpdated: parsed.lastUpdated ?? new Date().toISOString(),
      };
    }

    let emiWhere: any = {
      emi_date: { [Op.gte]: fromDate, [Op.lte]: toDate },
      [Op.or]: [
        { payment_status: '0' },
        { payment_done_date: { [Op.gte]: fromDate } },
      ],
    };
    if (screenShotBase == 1)
      emiWhere.adScreenshotUrl = {
        [Op.not]: null,
      };
    else if (screenShotBase == 2)
      emiWhere.adScreenshotUrl = {
        [Op.is]: null,
      };

    const allEmiData = await this.repository.getTableWhereDataWithCounts(
      attributes,
      { where: emiWhere },
    );
    if (allEmiData == k500Error) return kInternalError;

    const totalEMIIds = [...new Set(allEmiData.rows.map((el) => el.id))];

    const transInclude: SequelOptions = { model: TransactionEntity };
    transInclude.attributes = [
      'completionDate',
      'id',
      'emiId',
      'response',
      'status',
      'paidAmount',
      'utr',
      'subSource',
    ];

    let where: any = {};
    // Type -> TOTAL
    if (type == 'TOTAL') {
      transInclude.where = { subSource: kAutoDebit };
      where = { id: totalEMIIds };
      transInclude.required = false;
    }
    // Type -> PENDING
    else if (type == 'PENDING') {
      const attributes = ['emiId'];
      const options = {
        where: {
          emiId: { [Op.in]: totalEMIIds },
          status: kInitiated,
          subSource: kAutoDebit,
          type: kEMIPay,
        },
      };
      const transList = await this.transRepo.getTableWhereData(
        attributes,
        options,
      );
      if (transList == k500Error) return kInternalError;
      const targetEMIIds = transList.map((el) => el.emiId);
      where = {
        id: targetEMIIds,
        payment_status: '0',
      };
      transInclude.required = false;
    }
    // Type -> Manually paid via autodebit
    else if (type == kAutoDebit) {
      const attributes = ['emiId'];
      const options = {
        where: {
          emiId: { [Op.in]: totalEMIIds },
          status: kCompleted,
          subSource: kAutoDebit,
          type: kEMIPay,
        },
      };
      const transList = await this.transRepo.getTableWhereData(
        attributes,
        options,
      );
      if (transList == k500Error) return kInternalError;
      const targetEMIIds = transList.map((el) => el.emiId);
      where = {
        id: targetEMIIds,
        payment_status: '1',
      };
      transInclude.required = false;
    }
    // Type -> Manually paid via app
    else if (type == 'MANUAL') {
      const emiIds: any = await this.getPaidEMIs(reqData);
      const idArray = emiIds.map((el) => el.id);
      if (emiIds?.message) return emiIds;
      where = { id: idArray };
      transInclude.required = false;
      transInclude.where = { subSource: kAutoDebit, status: kCompleted };
    }
    // Type -> FAILED
    else if (type == kFailed) {
      transInclude.where = {
        subSource: kAutoDebit,
        [Op.or]: [
          { status: kFailed },
          { status: kInitiated, utr: { [Op.eq]: null } },
        ],
      };
      where = {
        id: totalEMIIds,
        payment_status: '0',
      };
    } else return k422ErrorMessage('Parameter type has invalid value');

    const loanInclude: any = {
      model: loanTransaction,
      attributes: [
        'appType',
        'balanceFetchDate',
        'currentAccountBalance',
        'id',
        'followerId',
        'loan_disbursement_date',
        'manualVerificationAcceptId',
        'insuranceId',
        'fullName',
        'phone',
        'bankingId',
        'subscriptionId',
        'predictionId',
      ],
      include: [
        {
          model: BankingEntity,
          attributes: ['mandateAccount', 'mandateBank', 'mandateIFSC'],
        },
        { model: disbursementEntity, attributes: ['id', 'amount'] },
        { model: SubScriptionEntity, attributes: ['id', 'mode'] },
        {
          required: false,
          model: PredictionEntity,
          attributes: ['id', 'categorizationTag'],
        },
      ],
    };
    if (searchText) {
      if (searchText.startsWith('l-'))
        where.loanId = searchText.replace('l-', '');
      else if (!isNaN(searchText)) {
        searchText = this.cryptService.encryptPhone(searchText);
        if (searchText == k500Error) return k500Error;
        searchText = searchText.split('===')[1];
        loanInclude.where = { phone: { [Op.like]: '%' + searchText + '%' } };
      } else loanInclude.where = { fullName: { [Op.iRegexp]: searchText } };
    }
    const include = [transInclude, loanInclude];
    const options1: any = {
      include,
      limit,
      offset,
      order: [['id', 'DESC']],
      where,
      useMaster: false,
    };
    if (needDownload) {
      delete options1.limit;
      delete options1.offset;
    }
    const emiData = await this.repository.getTableWhereDataWithCounts(
      attributes,
      options1,
    );
    if (emiData == k500Error) return kInternalError;

    /// Get insurance id(s) from data, when insurance id is found
    const ids = emiData.rows
      .filter((item) => item.loan.insuranceId !== null)
      .map((item) => item.loan.insuranceId);

    /// Get insurance data based on collected insurance id(s)
    let insuranceData = [];
    if (ids.length)
      insuranceData = await this.elephantService.funGetInsuranceData([
        ...new Set(ids),
      ]);

    const rows = [];
    const emiList = emiData.rows;
    const length = emiList.length;
    for (let index = 0; index < length; index++) {
      try {
        const emiData = emiList[index];
        let transList = emiData.transactionData ?? [];
        if (transList) transList.sort((a, b) => a.id - b.id);
        let transactionData: any = {};
        if (transList.length > 0) {
          transactionData = transList.find((el) => el.status == kCompleted);
          if (!transactionData) {
            transList = transList.filter((el) => el.subSource == kAutoDebit);
            if (transList.length > 0) transactionData = transList[0];
          }
        }
        const loanData = emiData.loan ?? {};

        let fetchTime = '-';
        if (loanData?.balanceFetchDate != null) {
          const dateStr = this.dateService.dateToReadableFormat(
            new Date(loanData?.balanceFetchDate),
          );
          fetchTime = `${dateStr.readableStr} ${dateStr.hours}:${dateStr.minutes} ${dateStr.meridiem}`;
        }

        let policyExpiryDate = '-';
        if (loanData?.insuranceId) {
          const data = insuranceData.find(
            (item) => loanData.insuranceId === item.id,
          );

          if (data?.id && data?.response) {
            const insurance = JSON.parse(data.response);
            const policyEndDate =
              insurance?.care?.policy_end_date ??
              insurance?.acko?.policy_end_date;
            policyExpiryDate = policyEndDate
              ? this.typeService.dateToJsonStr(policyEndDate)
              : '-';
          }
        }

        const disbList = loanData.disbursementData ?? [];
        let disbursementData: any = { amount: 0 };
        if (disbList.length > 0) disbursementData = disbList[0];
        const bankingData = loanData.bankingData ?? {};
        const subscriptionData = loanData.subscriptionData ?? {};
        const isPaid = emiData.payment_status == '1';
        const phone = this.cryptService.decryptPhone(loanData?.phone);

        const mobileNumber = maskOptions?.isMaskPhone
          ? this.cryptService.dataMasking('phone', phone)
          : phone;
        const accountNumber = bankingData?.mandateAccount ?? '-';

        const maskedAccountNumber = maskOptions?.isDisbursementAccMask
          ? this.cryptService.dataMasking('bankAccount', accountNumber)
          : accountNumber;
        const data = {
          userId: emiData.userId,
          Name: loanData.fullName ?? '-',
          'Mobile number': mobileNumber,
          'Loan ID': emiData.loanId,
          'Emi amount': Math.floor(
            emiData.principalCovered + emiData.interestCalculate,
          ),
          'Emi date': this.typeService.jsonToReadableDate(emiData.emi_date),
          'Current balance':
            loanData?.currentAccountBalance != null
              ? +loanData?.currentAccountBalance
              : '-',
          'Fetch time': fetchTime,
          'Emi paid date': '-',
          'Payment type': transactionData?.subSource ?? kAutoDebit,
          'Emi number': emiData.emiNumber ?? '-',
          'AD Placed amount':
            (transactionData?.paidAmount ?? '-').toString() ?? '-',
          UTR: transactionData?.utr ?? '-',
          "Today's EMI status": 'Response pending',
          'Payment Screenshot': '-',
          'AD Response date':
            this.typeService.jsonToReadableDate(
              transactionData?.completionDate,
            ) ?? '-',
          'Amount disbursed': Math.floor(
            disbursementData.amount / 100,
          ).toString(),
          'Disbursement date': this.typeService.jsonToReadableDate(
            loanData.loan_disbursement_date,
          ),
          'Bank name': bankingData.mandateBank ?? '-',
          IFSC: bankingData.mandateIFSC ?? '-',
          'Account number': maskedAccountNumber,
          'E-Mandate type': subscriptionData.mode ?? '-',
          Insurance: loanData?.insuranceId ? 'Yes' : 'No',
          'Insurance end date': policyExpiryDate,
          'Risk Category':
            loanData?.predictionData?.categorizationTag?.slice(0, -5) ?? '-',
          'Loan approved by':
            (
              await this.commonsharedService.getAdminData(
                loanData.manualVerificationAcceptId,
              )
            ).fullName ?? '-',
          'Follower name':
            (await this.commonsharedService.getAdminData(loanData.followerId))
              .fullName ?? '-',
          'AD Placed by': !transactionData
            ? 'system'
            : (
                await this.commonsharedService.getAdminData(
                  transactionData?.adminId,
                )
              ).fullName ?? '-',
          'Autodebit response': '',
          Platform:
            loanData?.appType == '1'
              ? EnvConfig.nbfc.nbfcShortName
              : process.env.APP_0,
        };
        if (isPaid) {
          data["Today's EMI status"] = kCompleted;
          data['Emi paid date'] = this.typeService.jsonToReadableDate(
            emiData.payment_done_date,
          );
          if (
            data['Payment type'] == kAutoDebit &&
            isPaid &&
            emiData['pay_type'] == kFullPay
          )
            data['Payment type'] = 'APP';
        } else if (!isPaid && type == kFailed) {
          data["Today's EMI status"] = kFailed;
          data['Autodebit response'] =
            this.commonsharedService.getFailedReason(
              transactionData?.response,
            ) ?? '-';
        } else if (!isPaid && type == 'TOTAL') {
          if (transactionData && transactionData?.status == kFailed) {
            data['Autodebit response'] =
              this.commonsharedService.getFailedReason(
                transactionData?.response,
              ) ?? '-';
            data["Today's EMI status"] = kFailed;
          } else if (!transactionData?.status)
            data["Today's EMI status"] = 'AD NOT PLACED';
        }

        if (type == 'PENDING')
          data['Payment Screenshot'] = emiData.adScreenshotUrl ?? '-';
        else delete data['Payment Screenshot'];
        rows.push(data);
      } catch (error) {}
    }

    await this.redisService.set(
      redisKey,
      JSON.stringify({ count: emiData.count, rows }),
      CACHE_TTL,
    );
    return { count: emiData.count, rows };
  }

  private async getCountsForRepaymentStatus(reqData) {
    try {
      let fromDate = reqData.fromDate;
      let toDate = reqData.endDate;
      if (!fromDate)
        fromDate = this.typeService.getGlobalDate(new Date()).toJSON();
      if (!toDate) toDate = this.typeService.getGlobalDate(new Date()).toJSON();
      const data = {
        totalCount: 0,
        totalAmount: 0,
        pendingCount: 0,
        pendingAmount: 0,
        autoDebitCount: 0,
        autoDebitAmount: 0,
        manualCount: 0,
        manualAmount: 0,
        failedCount: 0,
        failedAmount: 0,
        successRatio: 0,
      };

      // DB query
      const attributes: any = [
        [
          Sequelize.fn('SUM', Sequelize.col('principalCovered')),
          'principalAmount',
        ],
        [
          Sequelize.fn('SUM', Sequelize.col('interestCalculate')),
          'interestAmount',
        ],
      ];
      let options: any = {
        where: {
          emi_date: { [Op.gte]: fromDate, [Op.lte]: toDate },
          [Op.or]: [
            { payment_status: '0' },
            { payment_done_date: { [Op.gte]: fromDate } },
          ],
        },
      };
      const totalEMIData = await this.repository.getTableWhereDataWithCounts(
        attributes,
        options,
      );
      if (totalEMIData == k500Error) return kInternalError;
      data.totalCount = totalEMIData.count;
      data.totalAmount = totalEMIData.rows[0]?.principalAmount ?? 0;
      data.totalAmount += totalEMIData.rows[0]?.interestAmount ?? 0;

      // Paid manually from app (EMI PAY)

      const paidEMIs: any = await this.getPaidEMIs(reqData);
      if (paidEMIs?.message) return paidEMIs;
      data.manualCount = paidEMIs.length;
      const manualAmount = paidEMIs.reduce(
        (acc, curr) => acc + Number(curr.amount),
        0,
      );
      data.manualAmount = manualAmount;

      const autoDebitQuery = `SELECT count("EmiEntity"."id") AS "count",
      SUM("principalCovered") AS "principalAmount",
      SUM("interestCalculate") AS "interestAmount" FROM "EmiEntities" AS "EmiEntity"
      LEFT OUTER JOIN "TransactionEntities" AS "transactionData" ON "EmiEntity"."id" = "transactionData"."emiId"
      WHERE ("transactionData"."status" = 'COMPLETED' AND "transactionData"."subSource" = 'AUTODEBIT') AND
      ("EmiEntity"."emi_date" >= '${fromDate}' AND "EmiEntity"."emi_date" <= '${toDate}')
      AND "EmiEntity"."payment_done_date" >= '${fromDate}'`;

      const autodebitEMIdata = await this.repoManager.injectRawQuery(
        EmiEntity,
        autoDebitQuery,
      );

      if (autodebitEMIdata === k500Error) return kInternalError;
      data.autoDebitCount = Number(autodebitEMIdata[0]?.count) ?? 0;
      data.autoDebitAmount = Number(autodebitEMIdata[0]?.principalAmount) ?? 0;
      data.autoDebitAmount += Number(autodebitEMIdata[0]?.interestAmount) ?? 0;

      const pendingQuery = `SELECT count("EmiEntity"."id") AS "count", SUM("principalCovered")
      AS "principalAmount", SUM("interestCalculate") AS "interestAmount" FROM "EmiEntities" AS "EmiEntity"
      LEFT OUTER JOIN "TransactionEntities" AS "transactionData"
      ON "EmiEntity"."id" = "transactionData"."emiId" WHERE ("transactionData"."status" = 'INITIALIZED'
      AND "transactionData"."subSource" = 'AUTODEBIT') AND ("EmiEntity"."emi_date" >= '${fromDate}'
      AND "EmiEntity"."emi_date" <= '${toDate}') AND "EmiEntity"."payment_status" = '0';`;

      const unpaidEMIData = await this.repoManager.injectRawQuery(
        EmiEntity,
        pendingQuery,
      );
      if (unpaidEMIData === k500Error) return kInternalError;
      data.pendingCount = Number(unpaidEMIData[0]?.count) ?? 0;
      data.pendingAmount = Number(unpaidEMIData[0]?.principalAmount) ?? 0;
      data.pendingAmount += Number(unpaidEMIData[0]?.interestAmount) ?? 0;

      const failedQuery = `SELECT count("EmiEntity"."id") AS "count", SUM("principalCovered") AS "principalAmount",
      SUM("interestCalculate") AS "interestAmount" FROM "EmiEntities" AS "EmiEntity"
      LEFT OUTER JOIN "TransactionEntities" AS "transactionData" ON "EmiEntity"."id" = "transactionData"."emiId"
      WHERE (("transactionData"."status" = 'FAILED' AND "transactionData"."subSource" = 'AUTODEBIT'
      OR "transactionData"."status" = 'INITIALIZED' AND "transactionData"."subSource" = 'AUTODEBIT'
      AND "transactionData"."utr" is NULL)) AND ("EmiEntity"."emi_date" >= '${fromDate}'
      AND "EmiEntity"."emi_date" <= '${toDate}') AND "EmiEntity"."payment_status" = '0';`;

      const failedEMIData = await this.repoManager.injectRawQuery(
        EmiEntity,
        failedQuery,
      );
      if (failedEMIData === k500Error) return kInternalError;
      data.failedCount = Number(failedEMIData[0]?.count) ?? 0;
      data.failedAmount = Number(failedEMIData[0]?.principalAmount) ?? 0;
      data.failedAmount += Number(failedEMIData[0]?.interestAmount) ?? 0;

      // Fine tune
      data.totalAmount = Math.floor(data.totalAmount);
      data.successRatio = Math.floor(
        ((data.manualCount + data.autoDebitCount) * 100) / data.totalCount,
      );
      if (
        data.totalAmount === 0 &&
        data.manualAmount === 0 &&
        data.autoDebitAmount === 0
      )
        data.successRatio = 0;
      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async getPaidEMIs(reqData) {
    let fromDate = reqData.fromDate;
    let toDate = reqData.endDate;
    if (!fromDate)
      fromDate = this.typeService.getGlobalDate(new Date()).toJSON();
    if (!toDate) toDate = this.typeService.getGlobalDate(new Date()).toJSON();

    let attributes = ['id', 'emi_amount'];
    let options: any = {
      where: {
        emi_date: { [Op.gte]: fromDate, [Op.lte]: toDate },
        payment_done_date: { [Op.gte]: fromDate },
        pay_type: kFullPay,
      },
    };
    const fullPayEMIList = await this.repository.getTableWhereData(
      attributes,
      options,
    );
    if (fullPayEMIList == k500Error) return kInternalError;

    attributes = ['emiId'];
    const emiInclude: SequelOptions = { model: EmiEntity };
    emiInclude.attributes = ['id', 'emi_amount'];
    emiInclude.where = {
      emi_date: { [Op.gte]: fromDate, [Op.lte]: toDate },
      payment_done_date: { [Op.gte]: fromDate },
    };
    const include = [emiInclude];
    options = {
      include,
      where: {
        status: kCompleted,
        subSource: { [Op.in]: ['APP', 'WEB', kDirectBankPay] },
        type: { [Op.in]: [kEMIPay, 'PARTPAY'] },
      },
    };
    const transList = await this.transRepo.getTableWhereData(
      attributes,
      options,
    );

    const emiPayEmiIds = transList.map((el) => el.emiData);
    emiPayEmiIds.sort();
    let combinedEmiIds = [...fullPayEMIList, ...emiPayEmiIds];
    const uniqueCombinations = [
      ...new Set(combinedEmiIds.map((obj) => `${obj.id}-${obj.emi_amount}`)),
    ];
    const uniqueObjects = uniqueCombinations.map((combination) => {
      const [id, amount] = combination.split('-');
      return { id: id, amount: amount };
    });
    return uniqueObjects;
  }

  async sendUpcomingNotifyEmis(emiData) {
    try {
      // For LSP
      const LSP_template = await this.commonsharedService.getEmailTemplatePath(
        kEmailPaymentReminder,
        0, // appType,
        null,
        null,
      );
      const htmlDataLSP: any = fs.readFileSync(LSP_template, 'utf-8');
      // For NBFC
      const NBFC_template = await this.commonsharedService.getEmailTemplatePath(
        kEmailPaymentReminder,
        1, // appType,
        null,
        null,
      );
      const htmlDataNBFC: any = fs.readFileSync(NBFC_template, 'utf-8');

      const htmlTemplateArray = [htmlDataLSP, htmlDataNBFC];

      const hashPhones = emiData.map((item) => item?.user?.hashPhone);
      const nonWhatsAppHashPhone =
        await this.whatsAppService.getNonWhatsAppUsers(hashPhones);

      for (let i = 0; i < emiData.length; i++) {
        try {
          const emi = emiData[i];
          const appType = emi?.loan?.appType;
          const htmlData: any = htmlTemplateArray[appType] ?? '';
          const userData = emi.user;
          if (
            !gIsPROD &&
            !(
              userData.email.includes(
                EnvConfig.emailDomain.companyEmailDomain1,
              ) ||
              userData.email.includes(EnvConfig.emailDomain.companyEmailDomain2)
            )
          )
            continue;
          userData.phone = this.cryptService.decryptPhone(userData.phone);
          const key = emi.loanId * 484848;
          const payment_redirect = nPaymentRedirect + key;
          const smsOptions = {
            smsId:
              appType == 1
                ? kMsg91Templates.PAYMENT_REMINDER
                : kLspMsg91Templates.PAYMENT_REMINDER,
            name: userData.fullName,
            amount: this.typeService.amountNumberWithCommas(
              Math.floor(
                (emi?.principalCovered ?? 0) +
                  (emi?.interestCalculate ?? 0) -
                  (emi?.paid_principal ?? 0) -
                  (emi?.paid_interest ?? 0) +
                  (emi?.penalty ?? 0) +
                  (emi?.regInterestAmount ?? 0) -
                  (emi?.paidRegInterestAmount ?? 0) +
                  (emi?.bounceCharge ?? 0) +
                  (emi?.gstOnBounceCharge ?? 0) -
                  (emi?.paidBounceCharge ?? 0) +
                  (emi?.dpdAmount ?? 0) +
                  (emi?.penaltyChargesGST ?? 0) -
                  (emi?.paidPenalCharge ?? 0) +
                  (emi?.legalCharge ?? 0) +
                  (emi?.legalChargeGST ?? 0) -
                  (emi?.paidLegalCharge ?? 0),
              ),
            ),
            date: this.typeService.dateToFormatStr(emi.emi_date),
            payment_link: payment_redirect,
            short_url: '1',
            loanId: emi.loanId,
            appType,
          };
          this.allSmsService.sendSMS(userData.phone, MSG91, smsOptions);
          await this.sendPaymentReminderMail(emi, htmlData);

          if (nonWhatsAppHashPhone.includes(userData.hashPhone)) continue;
          await this.sendPaymentReminderWhatsAppMsg(emi);
        } catch (error) {}
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async sendPaymentReminderMail(emi, htmlData) {
    try {
      const userData = emi.user;
      const due_amount =
        (emi?.principalCovered ?? 0) +
        (emi?.interestCalculate ?? 0) -
        (emi?.paid_principal ?? 0) -
        (emi?.paid_interest ?? 0) +
        (emi?.penalty ?? 0) +
        (emi?.regInterestAmount ?? 0) -
        (emi?.paidRegInterestAmount ?? 0) +
        (emi?.bounceCharge ?? 0) +
        (emi?.gstOnBounceCharge ?? 0) -
        (emi?.paidBounceCharge ?? 0) +
        (emi?.dpdAmount ?? 0) +
        (emi?.penaltyChargesGST ?? 0) -
        (emi?.paidPenalCharge ?? 0) +
        (emi?.legalCharge ?? 0) +
        (emi?.legalChargeGST ?? 0) -
        (emi?.paidLegalCharge ?? 0);
      const loanId = emi.loanId;
      let due_date: any = this.dateService.dateToReadableFormat(emi?.emi_date);
      const appType = emi?.loan?.appType;
      const key = loanId * 484848;
      const payment_redirect = nPaymentRedirect + key;
      htmlData = htmlData.replace('##NAME##', userData.fullName);
      htmlData = htmlData.replace('##LOANID##', loanId);
      htmlData = htmlData.replace('##DUEDATE##', due_date.readableStr);
      htmlData = htmlData.replace('##DUEAMOUNT##', due_amount);
      htmlData = htmlData.replace('##NBFCINFO##', nbfcInfoStr);
      htmlData = htmlData.replace('##link##', payment_redirect);
      htmlData = this.replaceAll(
        htmlData,
        '##NBFCSHORTNAME##',
        EnvConfig.nbfc.nbfcCamelCaseName,
      );
      htmlData = htmlData.replaceAll(
        '##COLLECTIONEMAIL##',
        EnvConfig.mail.collectionEmail,
      );
      htmlData = htmlData.replaceAll(
        '##COLLECTIONCONTACT##',
        EnvConfig.number.collectionNumber,
      );
      htmlData = htmlData.replace(
        '##NBFCPAYMENTBANNER##',
        EnvConfig.url.nbfcPaymentBanner,
      );
      let replyTo = kCollectionEmail;
      let fromMail = kNoReplyMail;
      if (appType == '0') {
        fromMail = kLspNoReplyMail;
        replyTo = klspCollectionMail;
      }
      await this.sharedNotification.sendMailFromSendinBlue(
        userData.email,
        'Payment Reminder',
        htmlData,
        userData.id,
        [],
        [],
        fromMail,
        replyTo,
      );
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async sendPaymentReminderWhatsAppMsg(emiData) {
    const queryParams = new URLSearchParams({
      key: (emiData.loanId * 484848)?.toString(),
      whatsApp: '1',
    });
    const whatsAppMsgBody = {
      customerName: emiData?.user?.fullName,
      userId: emiData?.user?.id,
      number: emiData?.user?.phone,
      email: emiData?.user?.email,
      title: 'EMI Payment Reminder',
      emiAmount: this.typeService.amountNumberWithCommas(emiData?.emi_amount),
      loanId: emiData?.loanId,
      emiDate: this.typeService.getDateFormated(emiData?.emi_date),
      payKey: `payments?${queryParams?.toString()}`,
      appType: emiData?.loan?.appType,
      nbfcType: EnvConfig.nbfc.nbfcType,
    };
    await this.whatsAppService.sendWhatsAppMessageMicroService(whatsAppMsgBody);
  }

  async updateAllEmiDues(loanId = null) {
    try {
      const today = this.typeService.getGlobalDate(new Date());
      const options: any = {
        where: {
          payment_status: '0',
          payment_due_status: { [Op.ne]: '1' },
          emi_date: { [Op.lt]: today.toJSON() },
        },
      };
      if (loanId) options.where.loanId = loanId;
      const emiData = await this.repository.getTableWhereData(
        ['id', 'loanId'],
        options,
      );
      if (emiData === k500Error) return kInternalError;
      const loanIds: any[] = [...new Set(emiData.map((item) => item.loanId))];
      const id = emiData.map((e) => e?.id);
      const updatedRes = await this.repository.updateRowData(
        { payment_due_status: '1' },
        id,
      );

      await this.calculation.calculateCLTV({ loanIds });

      if (loanId) return {};
      // slack message in cron-alert
      const slackPayload = {
        url: 'admin/emi/updateAllEmiDues',
        fieldObj: {
          id,
          updatedRes,
        },
      };
      this.slackService.sendSlackCronAlert(slackPayload);
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async updateAllEmiDuesPenalty(query) {
    try {
      const loanId = query?.loanId ?? null;
      const defaulters = await this.getAllDefaulters(loanId);
      if (defaulters?.message) return defaulters;
      const blockUsers = [];
      const blockUsersHistory = [];
      const defaultersLength = defaulters.length;
      let totalEmiDues = 0;
      let dataUpdated = 0;
      let poolAndCollectionAdminLoanData = {};

      for (let i = 0; i < defaultersLength; i++) {
        try {
          const ele = defaulters[i];
          const emiId = ele.id;
          const userId = ele.userId;
          const loanId = ele.loanId;
          const user = ele.user;
          const loan = ele.loan;
          const emiData = loan.emiData;
          const followerId = loan?.followerId;
          let principalAmount = ele?.principalCovered ?? 0;
          let remainingPrincipal = ele?.principalCovered ?? 0;
          const transData = ele?.transactionData;
          // dpd amount
          let dpd_Amount = ele?.dpdAmount ?? 0;
          let transPrincipalAmount = 0;
          const penaltyChargeUpdate = ele?.penaltyCharges ?? {};
          const modifyCalculation =
            loan?.penaltyCharges?.MODIFICATION_CALCULATION ?? false;

          //update principal amount if part payment
          transData.filter((el) => {
            if (el?.principalAmount > 0) {
              transPrincipalAmount += el?.principalAmount ?? 0;
              // if any partpay is done on emi date
              if (
                new Date(el.completionDate).getTime() ==
                new Date(emiData.emi_date).getTime()
              ) {
                remainingPrincipal -= el?.principalAmount ?? 0;
              }
            }
          });
          principalAmount = principalAmount - transPrincipalAmount;

          const isBlacklist = user?.isBlacklist ?? '0';
          const emiAmount = parseFloat((+ele.emi_amount).toFixed(2));
          const emiDate = ele.emi_date;
          const toDay = new Date();

          const diffDays = this.typeService.differenceInDays(emiDate, toDay);

          if (diffDays == 1 && followerId) {
            if (poolAndCollectionAdminLoanData[followerId]) {
              poolAndCollectionAdminLoanData[followerId].push(loanId);
            } else {
              poolAndCollectionAdminLoanData[followerId] = [loanId];
            }
          }
          if (diffDays > 0) {
            totalEmiDues++;
            const currDelayDays = emiData.reduce(
              (prev, curr) => prev + (curr?.penalty_days ?? 0),
              0,
            );
            // If penalty is more than 5 days then user will be blacklisted
            if (
              (diffDays >= 5 || currDelayDays >= 5) &&
              isBlacklist != '1' &&
              !blockUsers.includes(userId) &&
              !kDummyUserIds.includes(userId)
            ) {
              blockUsers.push(userId);
              blockUsersHistory.push({
                userId,
                isBlacklist: '1',
                blockedBy: SYSTEM_ADMIN_ID,
                reasonId: 72,
                reason: kDelay5Days,
              });
            }
            const interestRate = parseFloat(loan.interestRate);
            const isUpdate = ele.penalty_update_date
              ? ele.penalty_update_date ==
                this.typeService.getGlobalDate(toDay).toJSON().substring(0, 10)
                ? false
                : true
              : true;

            // get new logic penalty amount
            const penaltyData = this.calcuPenaltyCharges(
              loan,
              principalAmount,
              remainingPrincipal,
              transData,
              emiDate,
              diffDays,
              penaltyChargeUpdate,
              dpd_Amount,
            );
            // penaltyCharge as per DPD days
            if (penaltyData.key == 'DPD_1_TO_3')
              penaltyChargeUpdate.DPD_1_TO_3 = true;
            else if (penaltyData.key == 'DPD_4_TO_14')
              penaltyChargeUpdate.DPD_4_TO_14 = true;
            else if (penaltyData.key == 'DPD_15_TO_30')
              penaltyChargeUpdate.DPD_15_TO_30 = true;
            else if (penaltyData.key == 'DPD_31_TO_60')
              penaltyChargeUpdate.DPD_31_TO_60 = true;
            else if (penaltyData.key == 'DPD_MORE_THAN_61')
              penaltyChargeUpdate.DPD_MORE_THAN_61 = true;

            // this penalty + interest
            let amount =
              isUpdate && !modifyCalculation
                ? (emiAmount * (interestRate * 2)) / 100
                : 0;

            const penalty = +((ele?.penalty ?? 0) + amount).toFixed(2);
            const totalPenalty = +((ele?.totalPenalty ?? 0) + amount).toFixed(
              2,
            );
            const updatedData = {
              penalty,
              totalPenalty,
              penalty_days: diffDays,
              penalty_update_date: this.typeService
                .getGlobalDate(toDay)
                .toJSON()
                .substring(0, 10),
              penaltyCharges: penaltyChargeUpdate,
              dpdAmount: modifyCalculation ? penaltyData.dpdAmount ?? 0 : 0,
              regInterestAmount: modifyCalculation
                ? penaltyData.regIntAmount ?? 0
                : 0,
            };
            const updateRes = await this.repository.updateRowData(
              updatedData,
              emiId,
            );

            const totalRepay: any = await this.totalRepayAmount(userId, loanId);
            if (totalRepay?.message) return totalRepay;
            const TotalRepayAmount = totalRepay?.totalRepayAmount;
            const loanUpdate: any = { TotalRepayAmount };

            //for collection requirment 07/02/2025
            emiData.sort((a, b) => a.id - b.id);
            const penaltyDaysList = emiData.filter(
              (el) =>
                el.payment_due_status === '1' && el.payment_status === '0',
            )[0];

            if (penaltyDaysList?.penalty_days == 1 && followerId) {
              const adminData = await this.commonsharedService.getAdminData(
                followerId,
              );
              if (adminData?.otherData?.eligibleForDPD1 == false) {
                loanUpdate.followerId = null;
                const createData = {
                  userId,
                  loanId,
                  type: 'Defaulter',
                  subType: kChangeFollowerId,
                  oldData: followerId,
                  newData: '',
                  adminId: SYSTEM_ADMIN_ID,
                };
                await this.changeLogsRepo.create(createData);
              }
            }
            const updateResponse = await this.loanRepo.updateRowData(
              loanUpdate,
              loanId,
            );
            if (
              Array.isArray(updateRes) &&
              Array.isArray(updateResponse) &&
              updateRes?.length &&
              updateResponse?.length &&
              updateRes[0] == 1 &&
              updateResponse[0] == 1
            )
              dataUpdated++;
          }
        } catch (error) {}
      }

      await this.userRepo.updateRowWhereData(
        { isBlacklist: '1' },
        { where: { id: blockUsers } },
      );
      await this.userBlockHistoryRepo.bulkCreate(blockUsersHistory);

      for (let [adminId, loanIds] of Object.entries(
        poolAndCollectionAdminLoanData,
      )) {
        try {
          await this.defaulterService.updateFollowerAndHistory(
            adminId,
            loanIds,
            SYSTEM_ADMIN_ID,
            '',
          );
        } catch (error) {}
      }

      if (loanId) return {};
      // slack for cron-alert
      const slackPayload = {
        url: 'admin/emi/updateAllEmiDuesPenalty',
        fieldObj: {
          defaultersLength,
          totalEmiDues,
          dataUpdated,
        },
      };

      this.slackService.sendSlackCronAlert(slackPayload);
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async getAllDefaulters(loanId = null) {
    try {
      const attributes = [
        'id',
        'emi_date',
        'emi_amount',
        'penalty',
        'totalPenalty',
        'penalty_days',
        'penalty_update_date',
        'loanId',
        'userId',
        'short_url',
        'emiNumber',
        'principalCovered',
        'penaltyCharges',
        'dpdAmount',
        'regInterestAmount',
      ];

      const transInclude = {
        model: TransactionEntity,
        attributes: ['id', 'principalAmount', 'completionDate'],
        required: false,
        where: { status: kCompleted },
        order: [['completionDate', 'ASC']],
      };
      const emiInc = {
        model: EmiEntity,
        attributes: [
          'id',
          'penalty_days',
          'loanId',
          'emi_amount',
          'totalPenalty',
          'penalty',
          'emiNumber',
          'emi_date',
          'penaltyCharges',
          'payment_status',
          'payment_due_status',
        ],
      };
      const include = [
        {
          model: loanTransaction,
          attributes: [
            'id',
            'netApprovedAmount',
            'interestRate',
            'duration',
            'penaltyCharges',
            'followerId',
          ],
          include: [emiInc],
        },
        {
          model: registeredUsers,
          attributes: ['id', 'fullName', 'email', 'phone', 'isBlacklist'],
        },
        transInclude,
      ];
      const options: any = {
        where: {
          payment_status: '0',
          payment_due_status: '1',
        },
        include,
        order: [['id', 'desc']],
      };

      if (loanId) {
        options.where.loanId = loanId;
      }

      const data = await this.repository.getTableWhereData(attributes, options);
      if (data === k500Error) return kInternalError;
      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private calcuPenaltyCharges(
    loan,
    principalAmount,
    remainingPrincipal,
    transData,
    emiDate,
    currDelayDays,
    penaltyChargeUpdate,
    dpd_Amount,
  ) {
    try {
      const interestRate = +loan?.interestRate;
      let key: any;

      if (
        currDelayDays >= 1 &&
        currDelayDays <= 3 &&
        penaltyChargeUpdate.DPD_1_TO_3 == false
      ) {
        key = 'DPD_1_TO_3';
      } else if (
        currDelayDays >= 4 &&
        currDelayDays <= 14 &&
        penaltyChargeUpdate.DPD_4_TO_14 == false
      ) {
        key = 'DPD_4_TO_14';
      } else if (
        currDelayDays >= 15 &&
        currDelayDays <= 30 &&
        penaltyChargeUpdate.DPD_15_TO_30 == false
      ) {
        key = 'DPD_15_TO_30';
      } else if (
        currDelayDays >= 31 &&
        currDelayDays <= 60 &&
        penaltyChargeUpdate.DPD_31_TO_60 == false
      ) {
        key = 'DPD_31_TO_60';
      } else if (
        currDelayDays > 60 &&
        penaltyChargeUpdate.DPD_MORE_THAN_61 == false
      ) {
        key = 'DPD_MORE_THAN_61';
      }

      let dpdAmount = 0;
      if (key) dpdAmount = (principalAmount * 5) / 100;

      // calculate dpd from
      let regIntAmount = 0;
      if (transData.length > 0) {
        for (let i = 0; i <= transData.length; i++) {
          const ele = transData[i];
          const diff_days = this.typeService.differenceInDays(
            i == transData.length
              ? this.typeService.getGlobalDate(new Date())
              : ele.completionDate,
            i == 0 ? emiDate : transData[i - 1].completionDate,
          );
          regIntAmount += (interestRate * remainingPrincipal * diff_days) / 100;
          if (i < transData.length) remainingPrincipal -= ele.principalAmount;
        }
      } else {
        const diff_days = this.typeService.differenceInDays(
          this.typeService.getGlobalDate(new Date()),
          emiDate,
        );
        regIntAmount += (interestRate * remainingPrincipal * diff_days) / 100;
      }

      dpdAmount = +(dpdAmount + dpd_Amount).toFixed(3);
      regIntAmount = +regIntAmount.toFixed(3);

      return { key, dpdAmount, regIntAmount };
    } catch (error) {}
  }

  async totalRepayAmount(userId, loanId) {
    try {
      const options = {
        where: { userId, loanId },
        order: [['id', 'ASC']],
      };
      const attr = [
        'id',
        'emi_amount',
        'penalty',
        'penalty_days',
        'payment_status',
        'payment_due_status',
      ];
      const emiData = await this.repository.getTableWhereData(attr, options);
      if (emiData === k500Error) return kInternalError;
      let totalRepayAmount = 0;
      let totalDueEMIAmount = 0;
      let totalDuePenalty = 0;
      let totalDueAmount = 0;
      let totalDueDay;
      for (let i = 0; i < emiData.length; i++) {
        try {
          const ele = emiData[i];
          const emiAmount = +ele.emi_amount;
          const penalty = ele?.penalty ? ele?.penalty : 0;
          const penaltyDays = ele?.penalty_days;
          totalRepayAmount = totalRepayAmount + emiAmount + penalty;
          if (ele?.payment_status == '0' && ele?.payment_due_status == '1') {
            totalDueAmount = totalDueAmount + emiAmount + penalty;
            totalDueEMIAmount = totalDueEMIAmount + emiAmount;
            totalDuePenalty = totalDuePenalty + penalty;
            if (!totalDueDay && penaltyDays) totalDueDay = penaltyDays;
          }
        } catch (error) {}
      }
      return {
        totalRepayAmount: this.typeService.manageAmount(totalRepayAmount),
        totalDueAmount: this.typeService.manageAmount(totalDueAmount),
        totalDueEMIAmount: this.typeService.manageAmount(totalDueEMIAmount),
        totalDuePenalty: this.typeService.manageAmount(totalDuePenalty),
        totalDueDay,
      };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region fun Send Over Due Notification
  async funSendOverDueNotification(query) {
    try {
      let options: any = {
        where: { payment_status: '0', payment_due_status: '1' },
        include: { model: loanTransaction, attributes: ['penaltyCharges'] },
      };
      if (query?.loanId) options.where.loanId = query?.loanId;

      let att = [
        'id',
        'loanId',
        'userId',
        'payment_status',
        'penalty_days',
        'loanId',
        'emi_amount',
        'emiNumber',
        'emi_date',
        'penalty',
        'principalCovered',
        'interestCalculate',
        'paid_principal',
        'paid_interest',
        'paid_penalty',
        'paidBounceCharge',
        'paidPenalCharge',
        'paidLegalCharge',
        'paidRegInterestAmount',
        'regInterestAmount',
        'bounceCharge',
        'gstOnBounceCharge',
        'dpdAmount',
        'penaltyChargesGST',
        'legalCharge',
        'legalChargeGST',
      ];
      const emiList = await this.repository.getTableWhereData(att, options);
      if (!emiList || emiList === k500Error) return kInternalError;
      emiList.forEach((emi) => {
        if (!emi?.loan?.penaltyCharges?.MODIFICATION_CALCULATION)
          emi.bounceCharge = 0;
      });

      const loanIds: any[] = [...new Set(emiList.map((item) => item.loanId))];
      options = { where: { id: loanIds } };
      att = [
        'id',
        'userId',
        'netApprovedAmount',
        'appType',
        'fullName',
        'email',
        'phone',
      ];
      const loanList = await this.loanRepo.getTableWhereData(att, options);
      if (!loanList || loanList === k500Error) return kInternalError;

      const finalData = [];
      for (let index = 0; index < loanIds.length; index++) {
        try {
          const loanId = loanIds[index];
          const emiData = emiList.filter((f) => f.loanId === loanId);
          emiData.sort((a, b) => a.id - b.id);
          const userId = emiData[0]?.userId;
          const loanData = loanList.find((f) => f.id === loanId);
          const temp: any = {};
          temp.loanId = emiData[0]?.loanId;
          temp.dueDate = emiData[0]?.emi_date;
          temp.userId = userId;
          temp.name = loanData?.fullName ?? '-';
          const approvedAmount = loanData?.netApprovedAmount ?? '-';
          if (approvedAmount != '-')
            temp.loanAmount = parseFloat(approvedAmount).toLocaleString();
          const appType = loanData?.appType;
          temp.appType = appType;
          /// get emi data
          let dueDay = 0;
          let dueAmount = 0;
          temp.contact = 'wa.link/x0k6kp';
          temp.brandName = kInfoBrandNameNBFC;
          emiData.forEach((ele) => {
            try {
              if (ele.payment_status == '0') {
                if ((ele?.penalty_days ?? 0) > dueDay)
                  dueDay = ele.penalty_days ?? 0;
                dueAmount +=
                  (ele?.principalCovered ?? 0) +
                  (ele?.interestCalculate ?? 0) -
                  (ele?.paid_principal ?? 0) -
                  (ele?.paid_interest ?? 0) +
                  (ele?.penalty ?? 0) +
                  (ele?.regInterestAmount ?? 0) -
                  (ele?.paidRegInterestAmount ?? 0) +
                  (ele?.bounceCharge ?? 0) +
                  (ele?.gstOnBounceCharge ?? 0) -
                  (ele?.paidBounceCharge ?? 0) +
                  (ele?.dpdAmount ?? 0) +
                  (ele?.penaltyChargesGST ?? 0) -
                  (ele?.paidPenalCharge ?? 0) +
                  (ele?.legalCharge ?? 0) +
                  (ele?.legalChargeGST ?? 0) -
                  (ele?.paidLegalCharge ?? 0);
              }
            } catch (error) {}
          });
          temp.totalAmountDue = (dueAmount ?? 0).toFixed(2);
          temp.dueday = dueDay;
          const key = loanId * 484848;
          temp.link = nPaymentRedirect + key;
          temp.email = loanData?.email;
          temp.phone = loanData?.phone;
          temp.emiData = emiData;
          finalData.push(temp);
        } catch (error) {}
      }
      await this.sendNotificationToAlldefaulters(finalData);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region send notification to all defaulters
  private async sendNotificationToAlldefaulters(list) {
    try {
      const listLength = list.length;
      for (let index = 0; index < listLength; index++) {
        try {
          const data = list[index];
          const userId = data.userId;
          const appType = data.appType;
          // send mail
          if (data?.email)
            await this.sharedNotification.sendEmail(
              kTOverDue,
              data.email,
              data,
            );

          //send sms
          const smsOptions = {
            name: data.name,
            dueamount: data.totalAmountDue,
            dueday: data.dueday,
            url: data.link,
            contact: data.contact,
            short_url: '1',
          };
          const title = `Your account is ${data.dueday} days past-due by Rs. ${data.totalAmountDue}`;
          const content = `Dear ${data.name}, Your account is ${data.dueday} days past-due by Rs. ${data.totalAmountDue} and request your immediate attention. Please make the payment ASAP or contact us on ${data.contact} to make necessary arrangements. Pay Now, Brand name : ${data.brandName}`;
          const userData = [];
          userData.push({ userId, appType });
          const body = {
            userData,
            content,
            title,
            isMsgSent: appType == 1 ? true : false,
            smsOptions,
            smsId: appType == 1 ? kMsg91Templates.EMIOverDueSMSID : null,
            // : kLspMsg91Templates.EMIOverDueSMSID,
          };
          await this.sharedNotification.sendNotificationToUser(body);
        } catch (error) {}
      }
    } catch (error) {}
  }
  //#endregion

  //#region -> Dashboard
  async statusInsights(reqData) {
    try {
      // Params validation
      const adminId = reqData.adminId;

      const isCountOnly = reqData.isCount == 'true';
      const type = reqData.type;
      const page = reqData.page;
      const minDate = reqData.minDate;
      const maxDate = reqData.maxDate;
      const isRefresh = reqData.isRefresh == 'true';

      if (!type && !isCountOnly) return kParamMissing('type');
      if (!page && !isCountOnly) return kParamMissing('page');

      const maskOptions = await this.commonsharedService.findMaskRole(adminId);
      const phoneMask = maskOptions?.isMaskPhone ? 'masked' : 'unmasked';
      const emailMask = maskOptions?.isMaskEmail ? 'masked' : 'unmasked';

      const redisKey = `STATUS_INSIGHTS_${adminId}_${type}_${page}_${isCountOnly}_${minDate}_${maxDate}_phone-${phoneMask}_email-${emailMask}`;
      const CACHE_TTL = 60 * 60;

      const cachedData = await this.redisService.get(redisKey);
      if (cachedData && !isRefresh) {
        const parsed = JSON.parse(cachedData);
        return {
          ...parsed,
          lastUpdated: parsed.lastUpdated ?? new Date().toISOString(),
        };
      }

      let result;
      // Get dashboard count
      if (isCountOnly) {
        result = await this.statusInsightsCount(reqData);
      }
      // Get details for particular data
      // 1 -> Pre EMI, 2 -> Due EMI, 3 -> Pre EMI

      // Pre EMI
      else if (type == '1') {
        result = await this.preEMIDetailsForDashboard(reqData, maskOptions);
      }
      // Dashboard paid on due day details
      else if (type == '2') {
        result = await this.paidOnDueDetailsForDashboard(reqData, maskOptions);
      }
      // Post EMI
      else if (type == '3') {
        result = await this.postEMIDetailsForDashboard(reqData, maskOptions);
      } else return kInvalidParamValue('type');

      const finalData = {
        ...result,
      };

      await this.redisService.set(
        redisKey,
        JSON.stringify(finalData),
        CACHE_TTL,
      );
      return finalData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  createBifurcationColumns(finalData) {
    for (let i = 0; i <= BIFURCATION_DAYS.length; i++) {
      if (i === BIFURCATION_DAYS.length) {
        finalData[`D${BIFURCATION_DAYS[i - 1]}+_count`] = 0;
        finalData[`D${BIFURCATION_DAYS[i - 1]}+_amount`] = 0;
      } else {
        finalData[`D${BIFURCATION_DAYS[i]}_count`] = 0;
        finalData[`D${BIFURCATION_DAYS[i]}_amount`] = 0;
      }
    }
  }

  getBifurcationDays(delayDays) {
    let foundKey = -1;

    for (let i = 0; i < BIFURCATION_DAYS.length; i++) {
      if (delayDays === BIFURCATION_DAYS[i]) {
        foundKey = BIFURCATION_DAYS[i];
        break;
      }
    }

    if (foundKey === -1) {
      foundKey = BIFURCATION_DAYS[BIFURCATION_DAYS.length - 1];
      return `D${foundKey}+`;
    }

    if (foundKey === 0) {
      return `D${foundKey}`;
    }

    return `D${foundKey}`;
  }

  prepareEMIBifergationCondition() {}

  // Dashboard pre emi details
  async preEMIDetailsForDashboard(reqData: any, adminId) {
    const maskOptions = await this.commonsharedService.findMaskRole(adminId);
    const isDownload = reqData?.download == 'true';
    const { minDate, maxDate } = reqData ?? {};
    const page = +(reqData?.page ?? 1);

    const searchData: any = await this.commonService.getSearchData(
      reqData?.searchText,
    );

    if (!minDate || !maxDate) {
      throw new Error('Both minDate and maxDate are required');
    }

    // Define the loanInclude structure
    const loanInclude: any = {
      model: loanTransaction,
      attributes: ['id', 'userId', 'loan_disbursement_date'],
      where: {},
    };

    if (searchData?.text != '' && searchData?.type == 'Name') {
      loanInclude.where = { fullName: { [Op.iRegexp]: searchData.text } };
    } // Search by user's phone number
    else if (searchData?.text != '' && searchData?.type == 'Number') {
      loanInclude.where = { phone: { [Op.like]: searchData.text } };
    } else if (searchData?.text != '' && searchData?.type == 'LoanId') {
      loanInclude.where.id = searchData.text;
    }

    // Modify the EMI query
    const emiAttributes = [
      'userId',
      'id',
      'emi_date',
      'pay_type',
      'fullPayPrincipal',
      'fullPayInterest',
      'fullPayPenalty',
      'principalCovered',
      'interestCalculate',
      'payment_status',
      'payment_due_status',
      'payment_done_date',
      'loanId',
      'paid_principal',
      'paid_interest',
      'penalty_days',
    ];

    const emiOptions: any = {
      where: {
        emi_date: {
          [Op.gte]: minDate,
          [Op.lte]: maxDate,
        },
      },
      group: ['EmiEntity.id', 'EmiEntity.userId', 'loan.id'],
      having: Sequelize.literal(`"payment_done_date" < "emi_date"`),
      include: [loanInclude],
      order: [['id', 'desc']],
    };

    if (!isDownload) {
      emiOptions.offset = (page - 1) * PAGE_LIMIT;
      emiOptions.limit = PAGE_LIMIT;
    }

    const emiData = await this.emiRepo?.getTableWhereDataWithCounts?.(
      emiAttributes,
      emiOptions,
    );
    if (emiData === k500Error) throw new Error('Failed to retrieve EMI data');

    const allLoanIds = [
      ...new Set(emiData?.rows?.map((el: any) => el?.loanId) ?? []),
    ];

    if (allLoanIds.length == 0) {
      return { rows: [], count: 0 };
    }

    const attributes = [
      'id',
      'paidAmount',
      'type',
      'penaltyAmount',
      'principalAmount',
      'interestAmount',
      'emiId',
      'loanId',
      'bounceCharge',
      'penalCharge',
      'legalCharge',
    ];
    const options = {
      where: {
        loanId: allLoanIds,
        status: kCompleted,
      },
    };
    const transList = await this.transRepo?.getTableWhereData?.(
      attributes,
      options,
    );

    if (transList === k500Error)
      throw new Error('Failed to retrieve transaction data');

    const loanTransactionMap = new Map();

    transList?.forEach?.((data: any) => {
      if (data?.loanId) {
        if (!loanTransactionMap.has(data.loanId)) {
          loanTransactionMap.set(data.loanId, [data]);
        } else {
          loanTransactionMap.get(data.loanId)?.push?.(data);
        }
      }
    });

    const allUserIds = [
      ...new Set(emiData?.rows?.map((el: any) => el?.userId) ?? []),
    ];

    const userAttributes = [
      'id',
      'email',
      'fullName',
      'phone',
      'completedLoans',
    ];
    const userOptions = {
      where: {
        id: allUserIds,
      },
    };

    const userData = await this.userRepo?.getTableWhereData?.(
      userAttributes,
      userOptions,
    );
    if (userData === k500Error) throw new Error('Failed to retrieve user data');

    // Create maps for quick lookup
    const userMap = new Map(
      userData?.map?.((user: any) => [user?.id, user]) ?? [],
    );

    const preparedList: any[] = [];
    const todayDate = this.typeService.getGlobalDate(new Date()).toJSON();

    emiData?.rows?.forEach?.((emi: any) => {
      const user: any = userMap.get(emi?.userId);

      const transactionData = loanTransactionMap.get(emi?.loanId);

      emi.loan = {
        ...(emi?.loan ?? {}),
        transactionData: transactionData ? [...transactionData] : [],
      };

      const data = this.newGetRateEMI(emi);

      const paidDateInfo = this.dateService.dateToReadableFormat(
        emi?.payment_done_date ?? todayDate,
      );
      const disbursedDateInfo = this.dateService.dateToReadableFormat(
        emi?.loan?.loan_disbursement_date ?? todayDate,
      );
      const emiDateInfo = this.dateService.dateToReadableFormat(
        emi?.emi_date ?? todayDate,
      );
      const paidAmount = (data?.paidPrincipal ?? 0) + (data?.paidInterest ?? 0);
      const phone = this.cryptService.decryptPhone(user?.phone ?? '');
      const maskedPhone = maskOptions?.isMaskPhone
        ? this.cryptService.dataMasking('phone', phone)
        : phone;

      const email = user?.email ?? '';
      const maskedEmail = maskOptions?.isMaskEmail
        ? this.cryptService.dataMasking('email', email)
        : email;
      preparedList.push({
        userId: user?.id ?? '',
        Name: user?.fullName ?? '',
        Phone: maskedPhone,
        Email: maskedEmail,
        'Loan Id': emi?.loanId,
        'Completed loans': user?.completedLoans ?? 0,
        'Emi date': emiDateInfo?.readableStr ?? '',
        'Repayment date': paidDateInfo?.readableStr ?? '',
        'Emi amount': Math.floor(paidAmount),
        'Disbursement date': disbursedDateInfo?.readableStr ?? '',
      });
    });

    if (isDownload) {
      const rawExcelData = {
        sheets: ['Pre emi users'],
        data: [preparedList],
        sheetName: 'Pre emi users.xlsx',
        needFindTuneKey: true,
      };
      const url = await this.fileService.objectToExcelURL(rawExcelData);
      if (url?.message) return url;
      return { fileUrl: url };
    }
    return { rows: preparedList, count: emiData?.count?.length ?? 0 };
  }

  // Dashboard post emi details
  private async postEMIDetailsForDashboard(reqData: any, adminId) {
    const maskOptions = await this.commonsharedService.findMaskRole(adminId);
    const isDownload = reqData?.download == 'true';
    const { minDate, maxDate } = reqData ?? {};
    const page = +(reqData?.page ?? 1);

    if (!minDate || !maxDate) {
      throw new Error('Both minDate and maxDate are required');
    }

    // Define the loanInclude structure
    const loanInclude: any = {
      model: loanTransaction,
      attributes: [
        'id',
        'userId',
        'loan_disbursement_date',
        'email',
        'fullName',
        'phone',
      ],
      where: {},
    };

    const searchData: any = await this.commonService?.getSearchData?.(
      reqData?.searchText,
    );

    if (searchData?.text != '' && searchData?.type == 'Name') {
      loanInclude.where = { fullName: { [Op.iRegexp]: searchData.text } };
    } // Search by user's phone number
    else if (searchData?.text != '' && searchData?.type == 'Number') {
      loanInclude.where = { phone: { [Op.like]: searchData.text } };
    } else if (searchData?.text != '' && searchData?.type == 'LoanId') {
      loanInclude.where.id = searchData.text;
    }

    const emiAttributes = [
      'userId',
      'id',
      'emi_date',
      'pay_type',
      'fullPayPrincipal',
      'fullPayInterest',
      'fullPayPenalty',
      'principalCovered',
      'interestCalculate',
      'payment_status',
      'payment_due_status',
      'payment_done_date',
      'loanId',
      'paid_principal',
      'paid_interest',
      'penalty_days',
    ];

    const emiOptions: any = {
      where: {
        emi_date: {
          [Op.gte]: minDate,
          [Op.lte]: maxDate,
        },
        penalty_days:
          reqData?.maxDelay == 4
            ? { [Op.gte]: reqData?.maxDelay }
            : reqData?.maxDelay == 1
            ? {
                [Op.or]: [{ [Op.eq]: reqData?.maxDelay }, { [Op.eq]: null }],
              }
            : { [Op.eq]: reqData?.maxDelay },
      },
      group: ['EmiEntity.id', 'EmiEntity.userId', 'loan.id'],
      having: Sequelize.or(
        Sequelize.literal(
          `"EmiEntity"."payment_done_date" > "EmiEntity"."emi_date"`,
        ),
        Sequelize.and(
          Sequelize.literal(`"EmiEntity"."payment_done_date" IS NULL`),
          Sequelize.or(
            Sequelize.literal(`"EmiEntity"."paid_principal" > 0`),
            Sequelize.literal(`"EmiEntity"."paid_interest" > 0`),
          ),
        ),
      ),
      include: [loanInclude],
      order: [['id', 'desc']],
    };

    if (!isDownload) {
      emiOptions.offset = (page - 1) * PAGE_LIMIT;
      emiOptions.limit = PAGE_LIMIT;
    }

    const emiData = await this.emiRepo?.getTableWhereDataWithCounts(
      emiAttributes,
      emiOptions,
    );
    if (emiData === k500Error) throw new Error('Failed to retrieve EMI data');

    const allLoanIds = [
      ...new Set(emiData?.rows?.map((el: any) => el?.loanId) ?? []),
    ];

    if (allLoanIds.length === 0) {
      return { rows: [], count: 0 };
    }

    const attributes = [
      'id',
      'paidAmount',
      'type',
      'penaltyAmount',
      'principalAmount',
      'interestAmount',
      'emiId',
      'loanId',
      'bounceCharge',
      'penalCharge',
      'legalCharge',
    ];
    const options = {
      where: {
        loanId: allLoanIds,
        status: kCompleted,
      },
    };
    const transList = await this.transRepo.getTableWhereData(
      attributes,
      options,
    );

    if (transList === k500Error)
      throw new Error('Failed to retrieve transaction data');

    const loanTransactionMap = new Map();

    transList?.forEach?.((data: any) => {
      if (data?.loanId) {
        if (!loanTransactionMap.has(data.loanId)) {
          loanTransactionMap.set(data.loanId, [data]);
        } else {
          loanTransactionMap.get(data.loanId).push(data);
        }
      }
    });

    const preparedList: any[] = [];
    const todayDate = this.typeService?.getGlobalDate?.(new Date())?.toJSON?.();

    emiData?.rows?.forEach((emi: any) => {
      const emiDateInfo = this.dateService.dateToReadableFormat(
        emi?.emi_date ?? todayDate,
      );
      let paidDateInfo: any = '';
      if (emi?.payment_done_date) {
        paidDateInfo = this.dateService.dateToReadableFormat(
          emi?.payment_done_date,
        );
      }
      const disbursedDateInfo = this.dateService.dateToReadableFormat(
        emi?.loan?.loan_disbursement_date ?? todayDate,
      );

      const transactionData = loanTransactionMap.get(emi?.loanId);

      emi.loan = {
        ...(emi?.loan ?? {}),
        transactionData: transactionData ? [...transactionData] : [],
      };

      const data = this.newGetRateEMI?.(emi);

      if (
        emi?.payment_done_date ||
        data.paymentStatus === 'POST_PAID' ||
        data.paymentStatus === 'PARTIAL_PAID' ||
        (data.paymentStatus === 'UN_PAID' && data?.totalPaid > 0)
      ) {
        const paidAmount =
          (data?.paidPrincipal ?? 0) + (data?.paidInterest ?? 0);
        const phone = this.cryptService.decryptPhone(emi?.loan?.phone ?? '-');
        const maskedPhone = maskOptions?.isMaskPhone
          ? this.cryptService.dataMasking('phone', phone)
          : phone;
        preparedList.push({
          userId: emi?.loan?.userId,
          Name: emi?.loan?.fullName ?? '-',
          Phone: maskedPhone,
          'Loan Id': emi.loanId,
          'Emi date': emiDateInfo?.readableStr ?? '',
          'Delay days': 'D+' + (emi?.penalty_days ?? 0),
          'Paid date': paidDateInfo?.readableStr ?? '',
          Amount: Math.floor(paidAmount),
          'Disbursement date': disbursedDateInfo?.readableStr ?? '',
        });
      }
    });

    // Generate excel
    if (isDownload) {
      const rawExcelData = {
        sheets: ['Post emi users'],
        data: [preparedList],
        sheetName: 'Post emi users.xlsx',
        needFindTuneKey: true,
      };
      const url: any = await this.fileService?.objectToExcelURL?.(rawExcelData);
      if (url?.message) return url;
      return { fileUrl: url };
    }
    return { rows: preparedList, count: emiData?.count?.length ?? 0 };
  }

  // Dashboard paid on due day details
  async paidOnDueDetailsForDashboard(reqData: any, adminId) {
    const maskOptions = await this.commonsharedService.findMaskRole(adminId);
    const isDownload = reqData?.download == 'true';
    const { minDate, maxDate } = reqData ?? {};
    const page = +(reqData?.page ?? 1);

    if (!minDate || !maxDate) {
      throw new Error('Both minDate and maxDate are required');
    }

    // Define the loanInclude structure
    const loanInclude: any = {
      model: loanTransaction,
      attributes: [
        'id',
        'userId',
        'loan_disbursement_date',
        'email',
        'fullName',
        'phone',
      ],
      where: {},
    };

    const searchData: any = await this.commonService.getSearchData(
      reqData?.searchText,
    );

    if (searchData?.text != '' && searchData?.type == 'Name') {
      loanInclude.where = { fullName: { [Op.iRegexp]: searchData.text } };
    } // Search by user's phone number
    else if (searchData?.text != '' && searchData?.type == 'Number') {
      loanInclude.where = { phone: { [Op.like]: searchData.text } };
    } else if (searchData?.text != '' && searchData?.type == 'LoanId') {
      loanInclude.where.id = searchData.text;
    }

    const emiAttributes = [
      'userId',
      'id',
      'emi_date',
      'pay_type',
      'fullPayPrincipal',
      'fullPayInterest',
      'fullPayPenalty',
      'principalCovered',
      'interestCalculate',
      'payment_status',
      'payment_due_status',
      'payment_done_date',
      'loanId',
      'paid_principal',
      'paid_interest',
      'penalty_days',
    ];

    const emiOptions: any = {
      where: {
        emi_date: {
          [Op.gte]: minDate,
          [Op.lte]: maxDate,
        },
      },
      group: ['EmiEntity.id', 'EmiEntity.userId', 'loan.id'],
      having: Sequelize.literal(`"payment_done_date" = "emi_date"`),
      include: [loanInclude],
      order: [['id', 'desc']],
    };

    if (!isDownload) {
      emiOptions.offset = (page - 1) * PAGE_LIMIT;
      emiOptions.limit = PAGE_LIMIT;
    }

    const emiData = await this.emiRepo.getTableWhereDataWithCounts(
      emiAttributes,
      emiOptions,
    );
    if (emiData === k500Error) throw new Error('Failed to retrieve EMI data');

    const allLoanIds = [
      ...new Set(emiData?.rows?.map((el: any) => el?.loanId) ?? []),
    ];

    if (allLoanIds.length === 0) {
      return { rows: [], count: 0 };
    }

    const attributes = [
      'id',
      'paidAmount',
      'type',
      'penaltyAmount',
      'principalAmount',
      'interestAmount',
      'emiId',
      'loanId',
      'bounceCharge',
      'penalCharge',
      'legalCharge',
    ];
    const options = {
      where: {
        loanId: allLoanIds,
        status: kCompleted,
      },
    };
    const transList = await this.transRepo.getTableWhereData(
      attributes,
      options,
    );

    if (transList === k500Error)
      throw new Error('Failed to retrieve transaction data');

    const loanTransactionMap = new Map();

    transList?.forEach?.((data: any) => {
      if (data?.loanId) {
        if (!loanTransactionMap.has(data.loanId)) {
          loanTransactionMap.set(data.loanId, [data]);
        } else {
          loanTransactionMap.get(data.loanId)?.push?.(data);
        }
      }
    });

    const preparedList: any[] = [];
    const todayDate = this.typeService?.getGlobalDate?.(new Date())?.toJSON?.();

    emiData?.rows?.forEach?.((emi: any) => {
      const emiDateInfo = this.dateService.dateToReadableFormat(
        emi?.emi_date ?? todayDate,
      );

      const transactionData = loanTransactionMap.get(emi?.loanId);
      const disbursedDateInfo = this.dateService.dateToReadableFormat(
        emi?.loan?.loan_disbursement_date ?? todayDate,
      );

      emi.loan = {
        ...(emi?.loan ?? {}),
        transactionData: transactionData ? [...transactionData] : [],
      };

      const data = this.newGetRateEMI?.(emi);

      if (data) {
        const paidAmount =
          (data?.paidPrincipal ?? 0) + (data?.paidInterest ?? 0);
        const phone = this.cryptService.decryptPhone(emi?.loan?.phone ?? '');
        const maskedPhone = maskOptions?.isMaskPhone
          ? this.cryptService.dataMasking('phone', phone)
          : phone;

        preparedList.push({
          userId: emi?.loan?.userId,
          Name: emi?.loan?.fullName ?? '',
          Phone: maskedPhone,
          'Loan Id': emi.loanId,
          'Emi date': emiDateInfo?.readableStr ?? '',
          'Paid amount': this.stringService.readableAmount(
            paidAmount,
            isDownload,
          ),
          'Disbursement date': disbursedDateInfo?.readableStr ?? '',
        });
      }
    });

    if (isDownload) {
      const rawExcelData = {
        sheets: ['On Time emi users'],
        data: [preparedList],
        sheetName: 'On Time emi users.xlsx',
        needFindTuneKey: true,
      };
      const url = await this.fileService?.objectToExcelURL?.(rawExcelData);
      if (url?.message) return url;
      return { fileUrl: url };
    }

    return { rows: preparedList, count: emiData?.count?.length ?? 0 };
  }

  // upcoming emi date total amount
  async upcomingEmiGetCount(query) {
    try {
      const start_date = this.typeService
        .getGlobalDate(query?.start_date ?? new Date())
        .toJSON();
      const end_date = this.typeService
        .getGlobalDate(query?.end_date ?? new Date())
        .toJSON();
      const where: any = {};
      const emiStatus = query?.emiStatus ?? '1';
      const cibilRiskCategory = query?.cibilRiskCategory ?? '-1';

      if (emiStatus == '3') {
        const data = await this.getDefulterData(where, start_date, end_date);
        if (data?.message) return data;
        if (data.length === 0) return { amount: 0 };
        where.loanId = data;
      }

      const whereClause = [
        `"EmiEntity"."payment_status" = '0'`,
        `"EmiEntity"."payment_due_status" = '0'`,
        `"EmiEntity"."emi_date" >= '${start_date}'`,
        `"EmiEntity"."emi_date" <= '${end_date}'`,
        ...(cibilRiskCategory !== '-1'
          ? [`"loan"."cibilBatchCategory" = ${parseInt(cibilRiskCategory)}`]
          : []),
        ...Object.entries(where).map(([key, value]) =>
          Array.isArray(value)
            ? `"EmiEntity"."${key}" IN (${value
                .map((v) => `'${v}'`)
                .join(',')})`
            : `"EmiEntity"."${key}" = '${value}'`,
        ),
      ].join(' AND ');

      const sqlQuery = `SELECT SUM(CAST("emi_amount" AS DOUBLE PRECISION)) AS "amount"
      FROM "EmiEntities" AS "EmiEntity"
      ${
        cibilRiskCategory !== '-1'
          ? 'INNER JOIN "loanTransactions" AS "loan" ON "EmiEntity"."loanId" = "loan"."id"'
          : ''
      }
      WHERE ${whereClause}
      LIMIT 1`;

      const queryData: any = await this.repoManager.injectRawQuery(
        EmiEntity,
        sqlQuery,
      );
      return queryData[0];
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion -> Dashboard
  //#endregion

  // Emi details as per date range
  async getEmidateRange(query) {
    const adminId = query.adminId;

    const start_date = this.typeService
      .getGlobalDate(query?.start_date ?? new Date())
      .toJSON();
    const end_date = this.typeService
      .getGlobalDate(query?.end_date ?? new Date())
      .toJSON();
    const page = +(query.page ?? 1);
    let searchText = query?.searchText;
    const download = query?.download;
    const whereName: any = {};
    const where: any = {};
    const emiStatus = +(query?.emiStatus ?? 1);
    const cibilRiskCategory = +(query?.cibilRiskCategory ?? -1);

    // In case this function is used by any other functionality too.
    // Currently using for Exotel api -> admin/defaulter/callDefaulters
    const dataWithoutLimit = query.dataWithoutLimit?.toString() == 'true';
    const extraColumns = query.extraColumns ?? [];

    if (searchText) {
      if (!isNaN(searchText)) {
        searchText = this.cryptService.encryptPhone(searchText);
        if (searchText == k500Error) return k500Error;
        searchText = searchText.split('===')[1];
        whereName.phone = { [Op.like]: '%' + searchText + '%' };
      } else whereName.fullName = { [Op.iRegexp]: searchText };
    }
    if (emiStatus == 3) {
      const data = await this.getDefulterData(where, start_date, end_date);
      if (data?.message) return data;
      if (data.length === 0) return { rows: [], count: 0 };
      where.loanId = data;
    }

    const cibilInclude = {
      model: CibilScoreEntity,
      attributes: ['plScore', 'cibilScore'],
      required: false,
    };

    // Preparation -> Query
    const subscriptionInclude: any = { model: SubScriptionEntity };
    subscriptionInclude.attributes = ['mode'];
    // Table join -> Bank
    const bankingInclude: SequelOptions = { model: BankingEntity };
    bankingInclude.attributes = [
      'adminSalary',
      'otherDetails',
      'salary',
      'salaryDate',
    ];
    bankingInclude.required = false;

    const loanWhere =
      cibilRiskCategory !== -1 ? { cibilBatchCategory: cibilRiskCategory } : {};
    const loanInclude = {
      model: loanTransaction,
      attributes: [
        'companyId',
        'id',
        'loan_disbursement_date',
        'loan_disbursement_id',
        'loanStatus',
        'netApprovedAmount',
        'mandate_id',
        'manualVerificationAcceptId',
        'insuranceId',
        'appType',
        'cibilBatchCategory',
        'bankingId',
        'subscriptionId',
        'predictionId',
        'cibilId',
        'userId',
        'phone',
        'fullName',
      ],
      where: { ...loanWhere, ...whereName },
      include: [
        bankingInclude,
        subscriptionInclude,
        {
          model: disbursementEntity,
          attributes: ['id', 'amount', 'bank_name', 'account_number', 'ifsc'],
        },
        {
          model: PredictionEntity,
          attributes: ['id', 'categorizationTag'],
        },
        cibilInclude,
      ],
    };

    const attributes = ['id', 'emi_date', 'emi_amount', 'loanId', 'emiNumber'];

    const options: any = {
      where: {
        ...where,
        payment_status: '0',
        payment_due_status: '0',
        emi_date: { [Op.gte]: start_date, [Op.lte]: end_date },
      },

      order: [['emi_date', 'ASC']],
      include: [loanInclude],
    };
    if (download != 'true' && !dataWithoutLimit) {
      options.offset = (page ?? 1) * PAGE_LIMIT - PAGE_LIMIT;
      options.limit = 1 * PAGE_LIMIT;
    }

    // Hit -> Query
    const getData = await this.repository.getTableWhereDataWithCounts(
      attributes,
      options,
    );
    // Validation -> Query data
    if (getData === k500Error) throw new Error();
    // Preparation -> API response
    const preparedatas = await this.prepareEmiDate(
      getData,
      extraColumns,
      adminId,
    );
    return { rows: preparedatas, count: getData.count };
  }
  //#end region

  //#region get defulter data
  async getDefulterData(where, start_date, end_date): Promise<any> {
    try {
      const options: any = {
        where: {
          ...where,
          payment_status: '0',
          payment_due_status: '0',
          emi_date: { [Op.gte]: start_date, [Op.lte]: end_date },
        },
      };
      const att = ['loanId'];
      const result = await this.repository.getTableWhereData(att, options);
      if (result === k500Error) return kInternalError;
      const loanIds = [...new Set(result.map((item) => item.loanId))];
      if (loanIds.length > 0) {
        const opt = {
          where: {
            loanId: loanIds,
            payment_status: '0',
            payment_due_status: '1',
          },
        };
        const findData = await this.repository.getTableWhereData(att, opt);
        if (findData === k500Error) return kInternalError;
        return [...new Set(findData.map((item) => item.loanId))];
      } else return [];
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  // prepare emidata
  private async prepareEmiDate(data, extraColumns = [], adminId) {
    const maskOptions = await this.commonsharedService.findMaskRole(adminId);
    const preparedData = [];
    const riskCategories = {
      0: 'Low Risk',
      1: 'Moderate Risk',
      2: 'High Risk',
    };

    let companyIds = data.rows.map((el) => el.loan?.companyId);
    companyIds = companyIds.filter((el) => el != undefined && el != null);
    const companyAttr = ['id', 'companyName'];
    const companyOptions = { where: { id: companyIds } };
    const companyList = await this.repoManager.getTableWhereData(
      GoogleCompanyResultEntity,
      companyAttr,
      companyOptions,
    );
    if (companyList == k500Error) throw new Error();

    /// Get insurance id(s) from data, when insurance id is found
    const ids = data.rows
      .filter((item) => item.loan.insuranceId !== null)
      .map((item) => item.loan.insuranceId);

    /// Get insurance data based on collected insurance id(s)
    let insuranceData = [];
    if (ids.length)
      insuranceData = await this.elephantService.funGetInsuranceData([
        ...new Set(ids),
      ]);

    const companyData = {};
    companyList.forEach((el) => {
      companyData[el['id']] = el['companyName'];
    });
    const length = data.rows.length;
    for (let i = 0; i < length; i++) {
      try {
        const element = data.rows[i];
        const loanData = element?.loan;
        let policyExpiryDate = '-';
        if (loanData?.insuranceId) {
          const data = insuranceData.find(
            (item) => loanData.insuranceId === item.id,
          );

          if (data?.id && data?.response) {
            const insurance = JSON.parse(data.response);
            const policyEndDate =
              insurance?.care?.policy_end_date ??
              insurance?.acko?.policy_end_date;
            policyExpiryDate = policyEndDate
              ? this.typeService.dateToJsonStr(policyEndDate)
              : '-';
          }
        }

        let id = element?.loan.manualVerificationAcceptId;
        let mobile = element?.loan?.phone;
        let phone = await this.cryptService.decryptPhone(mobile);
        const adminId = await this.commonsharedService.getAdminData(id);
        const appType = loanData?.appType;
        const cibilData = element?.loan?.cibilData;

        let cibilRiskCategory =
          riskCategories[loanData?.cibilBatchCategory] || '-';
        const phoneNumber = phone ?? '-';

        const maskedPhone = maskOptions?.isMaskPhone
          ? this.cryptService.dataMasking('phone', phoneNumber)
          : phoneNumber;
        const accountNumber =
          loanData?.disbursementData?.[0]?.account_number ?? '-';

        const maskedAccountNumber = maskOptions?.isDisbursementAccMask
          ? this.cryptService.dataMasking('bankAccount', accountNumber)
          : accountNumber;

        const passdata: any = {
          // 'CIBIL Risk category': cibilRiskCategory,
          userId: element?.loan?.userId ?? '-',
          Name: element?.loan?.fullName ?? '-',
          'Mobile Number': maskedPhone,
          'Loan Id': element?.loanId ?? '-',
          'App platform':
            loanData?.appType == 1
              ? EnvConfig.nbfc.nbfcShortName
              : EnvConfig.nbfc.appName,
          'Emi Amount': element?.emi_amount ?? '-',
          'Emi Type': 'EMI - ' + (element?.emiNumber ?? '-'),
          'Emi Date': this.typeService.dateToJsonStr(element?.emi_date),
          'Salary Date': loanData?.bankingData?.salaryDate ?? '-',
          'Approved Salary':
            loanData?.bankingData?.adminSalary ??
            loanData?.bankingData?.salary ??
            loanData?.bankingData?.otherDetails?.salary?.average ??
            '-',
          'Amount Disbursed': loanData.netApprovedAmount ?? '-',
          'Disbursedment Date': (
            loanData.loan_disbursement_date ?? '-'
          ).replace('T10:00:00.000Z', ''),
          'Bank Name': loanData.disbursementData[0].bank_name ?? '-',
          IFSC: loanData.disbursementData[0].ifsc ?? '-',
          'Account Number': maskedAccountNumber,
          'Emandate Type': loanData?.subscriptionData?.mode ?? '-',
          Insurance: loanData?.insuranceId ? 'Yes' : 'No',
          'Insurance End Date': policyExpiryDate,
          'Risk Category':
            loanData?.predictionData?.categorizationTag?.slice(0, -5) ??
            cibilRiskCategory,
          'Loan Approved By': adminId?.fullName ?? '-',
          'PL Score': cibilData?.plScore ?? '-',
          'Cibil Score': cibilData?.cibilScore ?? '-',
          appType,
          'Company name': companyData[loanData?.companyId ?? '-'] ?? '-',
        };
        if (extraColumns.includes('emiId')) passdata.emiId = element.id;
        preparedData.push(passdata);
      } catch (error) {}
    }
    return preparedData;
  }
  //#endRegion

  async checkPreEMIBalance(reqData) {
    try {
      // Params validation
      const adminId = reqData.adminId;
      if (!adminId) return kParamMissing('adminId');

      const targetDate = new Date();
      targetDate.setDate(targetDate.getDate() + 1);
      const emiDate = this.typeService.getGlobalDate(targetDate).toJSON();

      // Get upcoming EMIs
      let attributes = ['id', 'loanId'];
      let options: any = {
        where: { emi_date: emiDate, payment_status: '0' },
      };
      const emiList = await this.repository.getTableWhereData(
        attributes,
        options,
      );
      if (emiList == k500Error) return kInternalError;

      // Get target loans for upcoming EMIs
      const loanIds = emiList.map((el) => el.loanId);

      const bankingInclude: { model; attributes?; where? } = {
        model: BankingEntity,
      };
      const include = [bankingInclude];
      bankingInclude.attributes = ['id'];
      bankingInclude.where = { consentId: { [Op.ne]: null } };
      attributes = ['id'];
      options = { include, where: { id: loanIds, loanStatus: 'Active' } };
      // Query
      const loanList = await this.loanRepo.getTableWhereData(
        attributes,
        options,
      );
      if (loanList == k500Error) return kInternalError;

      // Balance fetch
      let failed = 0;
      let success = 0;
      const purpose = 3;
      for (let index = 0; index < loanList.length; index++) {
        const loanData = loanList[index];
        const loanId = loanData.id;
        const emiData = emiList.find((el) => el.loanId == loanId);
        if (!emiData) continue;

        const data = { adminId, emiId: emiData.id, loanId, purpose };
        const response = await this.bankingService.checkAABalance(data);
        if (response?.message) failed++;
        else success++;
      }

      return { failed, success };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // OCC 1.0.0 -> For the emi which is past due and unpaid then ECS bounce will get charged
  async addECSbounceCharge(reqData) {
    return {};
    const readOnly = reqData.readOnly === 'true';
    const transList = await this.getDataForECSBounceCharge();
    if (readOnly) return transList;
    return transList;
    // Update bounce charge
    const sessionId: string = uuidv4();
    const today = this.typeService.getGlobalDate(new Date()).toJSON();
    for (let index = 0; index < transList.length; index++) {
      try {
        const transData = transList[index];
        const emiData = transData?.emiData ?? {};
        const emiId = transData?.emiId;
        if (!emiId) continue;
        if (!transData.subscriptionDate) continue;
        if (transData.subscriptionDate >= today) continue;
        const response = transData?.response
          ? JSON.parse(transData?.response)
          : {};
        if (response?.adNotPlaced) continue;
        let gstOnBounceCharge = 0;
        let penalty = emiData.penalty ?? 0;
        let totalPenalty = emiData?.totalPenalty ?? 0;
        let bounceCharge = emiData?.bounceCharge ?? 0;
        let waived_bounce = emiData?.waived_bounce;
        if (waived_bounce) continue;

        if (emiData?.loan?.penaltyCharges?.MODIFICATION_CALCULATION) {
          gstOnBounceCharge = ECS_BOUNCE_CHARGE * 0.18;
        } else {
          penalty = +(penalty + ECS_BOUNCE_CHARGE).toFixed(2);
          totalPenalty = +(totalPenalty + ECS_BOUNCE_CHARGE).toFixed(2);
        }
        bounceCharge = ECS_BOUNCE_CHARGE;
        const systemCreationData = {
          sessionId,
          type: 1,
          emiId,
          loanId: transData.loanId,
          userId: transData.userId,
          uniqueId: `TYPE=${1}=EMI=` + emiId,
        };
        const createdData = await this.repoManager.createRowData(
          SystemTraceEntity,
          systemCreationData,
        );
        if (createdData === k500Error) continue;

        // Update -> EMI row data
        await this.repoManager.updateRowData(
          EmiEntity,
          {
            penalty,
            totalPenalty,
            gstOnBounceCharge,
            bounceCharge,
          },
          transData.emiId,
          // true,
        );
      } catch (error) {}
    }

    return {};
  }

  private async getDataForECSBounceCharge() {
    // Preparation -> Query
    const fromDate = new Date();
    fromDate.setDate(fromDate.getDate() - 5);
    const toDate = new Date();
    const range = this.typeService.getUTCDateRange(
      fromDate.toString(),
      toDate.toString(),
    );
    const today = this.typeService.getGlobalDate(new Date()).toJSON();
    const emiInc: SequelOptions = {
      model: EmiEntity,
      attributes: ['penalty', 'totalPenalty', 'waived_bounce'],
      where: {
        emi_date: {
          [Op.lt]: today,
          [Op.gte]: this.typeService.getGlobalDate(fromDate).toJSON(),
        },
        payment_status: '0',
        bounceCharge: 0,
      },
      required: true,
    };
    const attr = ['emiId', 'loanId', 'subscriptionDate', 'userId', 'response'];
    const ops = {
      where: {
        status: { [Op.in]: [kInitiated, kFailed] },
        type: kEMIPay,
        subSource: kAutoDebit,
        adminId: SYSTEM_ADMIN_ID,
        updatedAt: { [Op.gte]: range.fromDate, [Op.lte]: range.endDate },
        subscriptionDate: { [Op.eq]: Sequelize.col('"emiData"."emi_date"') },
      },
      include: [emiInc],
    };
    // Hit -> Query
    const trasList = await this.transRepo.getTableWhereData(attr, ops);
    // Validation -> Query data
    if (trasList === k500Error) throw new Error();

    return trasList;
  }

  async funFilteredUserEMIDataForWhattsappMessage() {
    try {
      let tomorrowDate = new Date();
      tomorrowDate.setDate(tomorrowDate.getDate() + 1);
      tomorrowDate = this.typeService.getGlobalDate(tomorrowDate);
      const userInclude = {
        model: registeredUsers,
        attributes: ['id', 'fullName', 'phone', 'email', 'hashPhone'],
      };
      const attributes = [
        'id',
        'payment_due_status',
        'payment_status',
        'userId',
        'loanId',
        'emi_date',
        'emi_amount',
        'payment_done_date',
        'penalty_days',
      ];

      const loanInclude = {
        model: loanTransaction,
        attributes: ['appType'],
      };
      let options = {
        where: {
          emi_date: { [Op.eq]: tomorrowDate.toJSON() },
          payment_status: '0',
        },
        include: [userInclude, loanInclude],
      };
      //get all tomorrow emi users data
      const allTomorrowEMIDateUsers = await this.repository.getTableWhereData(
        attributes,
        options,
      );

      if (!allTomorrowEMIDateUsers || allTomorrowEMIDateUsers === k500Error)
        return kInternalError;
      if (allTomorrowEMIDateUsers?.message) return allTomorrowEMIDateUsers;
      await this.sendTomorrowEMIWhattsappNotification(allTomorrowEMIDateUsers);
      return allTomorrowEMIDateUsers;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async sendTomorrowEMIWhattsappNotification(allTomorrowEMIDateUsers) {
    try {
      const dataLength = allTomorrowEMIDateUsers?.length;
      const adminId = allTomorrowEMIDateUsers?.adminId ?? SYSTEM_ADMIN_ID;

      const hashPhones = allTomorrowEMIDateUsers.map(
        (item) => item.user?.hashPhone,
      );
      const nonWhatsAppHashPhone =
        await this.whatsAppService.getNonWhatsAppUsers(hashPhones);

      for (let i = 0; i < dataLength; i++) {
        try {
          const emi = allTomorrowEMIDateUsers[i];

          const userData = emi?.user;
          const loan = emi?.loan;
          const appType = loan?.appType;
          if (nonWhatsAppHashPhone.includes(userData?.hashPhone)) continue;
          let number = this.cryptService.decryptPhone(userData?.phone);
          const whattsappOptions = {
            customerName: userData?.fullName,
            email: userData?.email,
            number,
            amount: this.typeService.amountNumberWithCommas(
              Math.floor(+emi?.emi_amount),
            ),
            date: this.typeService.dateToFormatStr(emi?.emi_date),
            loanId: emi?.loanId,
            userId: emi?.userId,
            adminId,
            title: 'Payment reminder',
            requestData: 'payment_reminder',
            appType,
          };
          this.whatsAppService.sendWhatsAppMessageMicroService(
            whattsappOptions,
          );
        } catch (error) {
          this.errorContextService.throwAndSetCtxErr(error);
          return kInternalError;
        }
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  private escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& means the whole matched string
  }

  private replaceAll(str, find, replace) {
    return str.replace(new RegExp(this.escapeRegExp(find), 'g'), replace);
  }
  async checkCreatedEmis() {
    try {
      const today = new Date();
      const emiInclude: SequelOptions = { model: EmiEntity };
      emiInclude.attributes = ['id'];
      const include = [emiInclude];
      const attribute = ['id', 'netEmiData'];
      const options = {
        where: {
          loanStatus: 'Active',
          check_emi_creation_date: { [Op.eq]: null },
        },
        include,
        order: [['id', 'DESC']],
        limit: 50,
      };
      const data = await this.loanRepo.getTableWhereData(attribute, options);
      if (data == k500Error) return kInternalError;

      let MismatchedData = [];
      const Datalength = data.length;
      for (let i = 0; i < Datalength; i++) {
        try {
          const element = data[i];
          // match Emi and gernerated Emi's
          const netEmiData = element.netEmiData
            ? element.netEmiData.map(JSON.parse)
            : [];
          const emiIds = element.emiData
            ? element.emiData.map((emi) => emi.id)
            : [];

          if (
            netEmiData.length !== emiIds.length ||
            emiIds.length === 0 ||
            netEmiData.length === 0
          ) {
            MismatchedData.push(element);
          }

          // Update loan data
          const updatedData = { check_emi_creation_date: today.toJSON() };
          await this.loanRepo.updateRowData(updatedData, element.id);
        } catch (error) {}
      }

      const MismatchedIds = MismatchedData.map((item) => item.id);
      const htmlTemplate = fs.readFileSync(kMismatchedEmiCreation, 'utf-8');

      // Send Email If Mismatch Found
      let length = MismatchedIds.length;
      for (let i = 0; i < length; i++) {
        try {
          const ele = MismatchedIds[i];
          let htmlData = htmlTemplate ?? '';
          if (!htmlData) return k422ErrorMessage('Mail format not readable');
          htmlData = htmlData.replace('##LOANID##', ele);
          htmlData = htmlData.replace('##NBFC##', EnvConfig.nbfc.nbfcName);
          htmlData = htmlData.replace('##NBFCINFO##', nbfcInfoStr);
          htmlData = htmlData.replace(
            '##NBFCSHORTNAME##',
            EnvConfig.nbfc.nbfcCamelCaseName,
          );
          htmlData = this.replaceAll(htmlData, '##HELPCONTACT##', kHelpContact);
          const fromMail = kNoReplyMail;
          const replyTo = kSupportMail;
          await this.sharedNotification.sendMailFromSendinBlue(
            kTechSupportMail,
            'Urgent - EMI Mismatch Found',
            htmlData,
            null,
            [],
            [],
            fromMail,
            replyTo,
          );
        } catch (error) {}
      }
      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async todaysEmiCalling(query) {
    try {
      const adminId = query?.adminId;
      if (!adminId) return kParamMissing('adminId');
      const dateObj = new Date();
      let type = query?.category;
      const currentDate = this.typeService.getGlobalDate(dateObj).toJSON();
      const attributes = ['id', 'userId', 'loanId'];
      const userInclude = {
        model: registeredUsers,
        attributes: ['id', 'phone'],
      };
      const options: any = {
        where: {
          emi_date: {
            [Op.eq]: currentDate,
          },

          payment_status: '0',
        },
        include: [userInclude],
      };

      const transInclude: SequelOptions = { model: TransactionEntity };
      transInclude.attributes = [
        'completionDate',
        'id',
        'emiId',
        'response',
        'status',
        'paidAmount',
        'utr',
        'subSource',
      ];
      if (type == 'TODAY_EMI') {
        transInclude.where = {
          status: kInitiated,
          subSource: kAutoDebit,
          type: kEMIPay,
        };
        options.include.push(transInclude);
      } else if (type == 'TODAY_EMI_FAILED') {
        transInclude.where = {
          subSource: kAutoDebit,
          [Op.or]: [
            { status: kFailed },
            { status: kInitiated, utr: { [Op.eq]: null } },
          ],
        };
        options.include.push(transInclude);
      }

      const data: any = await this.emiRepo.getTableWhereData(
        attributes,
        options,
      );

      if (!data || data === k500Error) return kInternalError;
      const finalData: any = [];
      for (let i = 0; i < data.length; i++) {
        try {
          const element = data[i];
          const decryptedPhone = this.cryptService.decryptPhone(
            element?.user?.phone,
          );
          if (decryptedPhone === k500Error) continue;
          const passData = {
            phone: decryptedPhone, //des,
            adminId,
            userId: element?.userId,
            emiId: element?.id,
            loanId: element?.loanId,
          };

          finalData.push(passData);
        } catch (error) {
          this.errorContextService.throwAndSetCtxErr(error);
          return kInternalError;
        }
      }

      const callData = {
        category: type,
        targetList: finalData,
        adminId,
      };
      return await this.callingService.placeCall(callData);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // #startregion upcoming emi calling

  async upcomingEmiCalling(query) {
    try {
      if (!query?.adminId || !query?.startDate || !query?.endDate)
        return kParamsMissing;
      const passData = {
        adminId: query?.adminId,
        startDate: query?.startDate,
        endDate: query?.endDate,
      };

      const type = 'UPCOMING_EMI';
      const currentDate = this.typeService
        .getGlobalDate(passData?.startDate)
        .toJSON();
      const lastDate = this.typeService
        .getGlobalDate(passData?.endDate)
        .toJSON();
      const attributes = ['id', 'loanId', 'userId'];
      const userInclude = {
        model: registeredUsers,
        attributes: ['id', 'phone'],
      };
      const options: any = {
        where: {
          emi_date: {
            [Op.gte]: currentDate,
            [Op.lte]: lastDate,
          },
          payment_status: '0',
        },
        include: [userInclude],
      };
      const data: any = await this.emiRepo.getTableWhereData(
        attributes,
        options,
      );

      if (!data || data === k500Error) return kInternalError;
      const finalData = [];
      for (let i = 0; i < data.length; i++) {
        const ele = data[i];
        const decryptedPhone = this.cryptService.decryptPhone(ele?.user?.phone);
        if (decryptedPhone === k500Error) continue;
        const defaulterObj: any = {
          emiId: ele?.id,
          phone: decryptedPhone,
          userId: ele?.userId,
          loanId: ele?.loanId,
        };
        finalData.push(defaulterObj);
      }
      const callPassData = {
        category: type,
        adminId: passData?.adminId,
        targetList: finalData,
      };
      return await this.callingService.placeCall(callPassData);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#endregion

  //#region Tomorrow Emi Date AA users Auto debit SessionId Change
  async getPreEMIAAUsersData(reqData) {
    const isReadOnly =
      reqData.isReadOnly == true || reqData.isReadOnly == 'true';

    // Take Tomorrow Date
    let tomorrowDate = new Date();
    tomorrowDate.setDate(tomorrowDate.getDate() + 1);
    tomorrowDate = this.typeService.getGlobalDate(tomorrowDate);

    // Table join -> Bank
    const bankingInclude: SequelOptions = { model: BankingEntity };
    bankingInclude.attributes = [
      'id',
      'sessionId',
      'consentId',
      'consentHandleId',
      'consentPhone',
      'consentMode',
    ];
    bankingInclude.required = true;
    bankingInclude.where = {
      consentStatus: { [Op.or]: ['ACCEPTED', 'ACTIVE'] },
      consentId: { [Op.ne]: null },
      consentResponse: { [Op.ne]: null },
    };

    // Table join -> EMI
    const emiInlcude: SequelOptions = { model: EmiEntity };
    emiInlcude.attributes = [];
    emiInlcude.where = {
      payment_status: '0',
      emi_date: {
        [Op.eq]: tomorrowDate.toJSON(),
      },
    };
    // Query Preparation
    const options = {
      include: [bankingInclude, emiInlcude],
    };

    // Query Hit
    const data: any = await this.loanRepo.getTableWhereData(
      ['id', 'userId'],
      options,
    );

    if (!data || data === k500Error) throw new Error();

    const userIds = [...new Set(data.map((el) => el.userId))];

    const userAttr = ['id', 'typeOfDevice', 'masterId'];
    const userOptions: any = {
      where: { id: userIds },
    };
    const userData = await this.userRepo.getTableWhereData(
      userAttr,
      userOptions,
    );
    if (userData === k500Error) throw new Error();

    const masterIds = [...new Set(userData.map((el) => el.masterId))];

    const masterAttr = ['status', 'rejection', 'id'];
    const masterOptions = {
      where: { id: masterIds },
    };
    const masterData = await this.repoManager.getTableWhereData(
      MasterEntity,
      masterAttr,
      masterOptions,
    );
    if (masterData === k500Error) throw new Error();

    const masterMap = new Map(
      masterData.map((master: any) => [master.id, master]),
    );
    const userMap = new Map(
      userData.map((user: any) => {
        const masterDetails = masterMap.get(user.masterId);
        return [
          user.id,
          {
            ...user,
            masterData: masterDetails || {},
          },
        ];
      }),
    );

    if (isReadOnly) {
      return { isReadOnly, list: data };
    }

    // As per discussion with collection team,  We need to fetch only if more EMIs are there else not required
    if (data.length <= 250) {
      return { isFetched: false, isReadOnly };
    }

    for (let index = 0; index < data.length; index++) {
      try {
        console.log({ index, total: data.length });
        const bankingData = data[index]?.bankingData;
        const consentId = bankingData.consentId;
        const consentHandleId = bankingData.consentHandleId;
        const custId = await this.cryptService.decryptPhone(
          bankingData.consentPhone,
        );
        // Retrieve user data from the map
        const user: any = userMap.get(data[index]?.userId); // access userId from data[index]
        if (!user) continue;

        const typeOfDevice = user?.typeOfDevice;

        if (bankingData?.consentMode == kfinvu) {
          const consentData: any = await this.finvuService.checkConsentStatus(
            consentHandleId,
            `${custId}@finvu`,
          );

          if (
            typeOfDevice == '2' &&
            consentData?.consentId == null &&
            consentData?.consentStatus != 'REJECTED'
          ) {
            continue;
          }
          if (consentData.consentStatus !== 'ACCEPTED') continue;

          // Check consent expired or not
          const consentExpiry =
            await this.bankingSharedService.isFinvuConsentExpired(consentId);
          if (consentExpiry) continue;
          // Check if 3 attempts have been made in a month or not
          const consentLimit =
            await this.bankingSharedService.isFinvuConsentLimitExceeded(
              consentId,
            );
          if (consentLimit) continue;

          const sessionId = await this.finvuService.fetchDataRequest(
            custId,
            consentId,
            consentHandleId,
          );
          if (!sessionId) continue;

          // update session Id
          const updateData = await this.bankingRepo.updateRowData(
            { sessionId },
            bankingData.id,
          );
          if (updateData === k500Error) throw new Error();

          //Save the entry of request
          const creationData = {
            source: 'FINVU',
            data: data[index] ?? {},
            loanId: data[index]?.id,
            userId: data[index]?.userId,
            type: 1,
            subType: 4,
            createdAt: new Date().toJSON(),
          };
          await this.repoManager.createRowData(AALogs, creationData);
        } else if (bankingData?.consentMode == kCAMS) {
          const consentResponse = await this.camsService.checkConsentStatus(
            bankingData.consentHandleId,
            bankingData.consentId,
          );
          if (consentResponse?.message) return consentResponse;
          if (consentResponse.consentExpired == true) return {};

          const consentStatus = consentResponse?.consentDetails?.consentStatus;

          if (
            typeOfDevice == '2' &&
            consentResponse?.consentDetails?.consentId == null &&
            consentStatus != 'REJECTED'
          )
            continue;
          if (consentStatus !== kCapActive) continue;

          const sessionId = await this.camsService.fetchData(
            bankingData.consentId,
          );
          if (!sessionId) throw new Error();

          // update session Id
          const updateData = await this.bankingRepo.updateRowData(
            { sessionId },
            bankingData.id,
          );
          if (updateData === k500Error) throw new Error();

          //Save the entry of request
          const creationData = {
            source: kCAMS,
            data: data[index] ?? {},
            loanId: data[index]?.id,
            userId: data[index]?.userId,
            type: 1,
            subType: 4,
            createdAt: new Date().toJSON(),
          };
          await this.repoManager.createRowData(AALogs, creationData);
        }
      } catch (error) {}
    }

    return true;
  }
  //#endregion

  async loanDueAlert(reqData) {
    const interval = reqData?.days ?? 5;
    const sDate = new Date();
    sDate.setDate(sDate.getDate() - interval);
    const startDate = this.typeService.getGlobalDate(sDate).toJSON();
    const eDate = new Date();
    const endDate = this.typeService.getGlobalDate(eDate).toJSON();
    // Including Transaction Entity....
    const tranInc = {
      model: TransactionEntity,
      attributes: [
        'id',
        'loanId',
        'emiId',
        'status',
        'subSource',
        'type',
        'subscriptionDate',
      ],
      where: {
        status: kFailed,
        type: [kFullPay, kEMIPay],
        subSource: kAutoDebit,
        adminId: SYSTEM_ADMIN_ID,
        subscriptionDate: { [Op.gte]: startDate, [Op.lt]: endDate },
        subStatus: {
          [Op.or]: [{ [Op.eq]: null }, { [Op.ne]: 'AD_NOT_PLACED' }],
        },
        utr: { [Op.ne]: null },
      },
    };
    // Including EMI Entity....
    const emiInc = {
      model: EmiEntity,
      attributes: [
        'id',
        'payment_due_status',
        'bounceCharge',
        'emi_date',
        'waived_bounce',
      ],
      where: {
        payment_status: '0',
        emi_date: { [Op.gte]: startDate, [Op.lt]: endDate },
      },
      required: true,
    };
    const include = [tranInc, emiInc];
    const loanOpt = { where: { loanStatus: 'Active' }, include };
    const loanData = await this.loanRepo.getTableWhereData(['id'], loanOpt);
    if (loanData == k500Error) throw new Error();
    const loanIds = loanData.map((el) => el.id);
    const legalAttr = ['id', 'type', 'emiId', 'loanId'];
    const legalOps = { where: { loanId: loanIds, type: [1, 2] } };
    const legalData = await this.legalRepo.getTableWhereData(
      legalAttr,
      legalOps,
    );
    if (legalData == k500Error) throw new Error();
    let missedBounce = [];
    let missedEmi = [];
    let missedDemand = [];
    let missedLegal = [];
    const length = loanData.length;
    //Looping over loanData after joins....
    for (let i = 0; i < length; i++) {
      try {
        const loan = loanData[i];
        const loanId = loan.id;
        const transData = loan?.transactionData ?? [];
        const emiData = loan.emiData ?? [];

        // CHecking missed EMI Due charges....
        const emiDue = emiData.find((emi) => emi.payment_due_status == '0');
        if (emiDue) missedEmi.push(loanId);
        // checking missed ECS bounce charge....
        const emiAD = transData.find((trans) => trans.type == 'EMIPAY');
        if (!emiAD) continue;
        const emiBounce = emiData.find(
          (emi) =>
            emi.id == emiAD.emiId &&
            (emi?.bounceCharge ?? 0) + (emi?.waived_bounce ?? 0) == 0,
        );
        if (emiBounce) missedBounce.push(loanId);

        // Checking missed demand charges....
        const demand = legalData.find(
          (l) => l.type == 1 && l?.emiId == emiAD.emiId,
        );
        if (!demand) missedDemand.push(loanId);

        // Checking missed legal charges....
        const emiADFullPay = transData.find(
          (record) => record.type == 'FULLPAY',
        );
        const notice = legalData.find(
          (l) => l.type == 2 && l?.loanId == loanId,
        );
        if (emiADFullPay && !notice) missedLegal.push(loanId);
      } catch (error) {}
    }
    if (reqData.needData) {
      return {
        missedEmi,
        missedBounce,
        missedDemand,
        missedLegal,
      };
    }
    // slack message in cron-alert
    const slackPayload = {
      url: 'admin/emi/getLoanDueAlert',
      fieldObj: {
        missedEmi,
        missedBounce,
        missedDemand,
        missedLegal,
      },
    };

    this.slackService.sendSlackCronAlert(slackPayload);
    return {};
  }

  // Get dashboard count
  async statusInsightsCount(reqData) {
    const targetDate = reqData?.targetDate;
    if (!targetDate) return kParamMissing('targetDate');

    const monthRange = this.dateService.getMonthRange(targetDate);
    const minDate = monthRange.firstDate.toJSON();
    const lastDateOfMonth = monthRange.lastDate;
    const today = this.typeService.getGlobalDate(new Date());
    const maxDate =
      today.getTime() <= lastDateOfMonth.getTime()
        ? today.toJSON()
        : monthRange.lastDate.toJSON();

    const emiAttributes = [
      'userId',
      'id',
      'emi_date',
      'pay_type',
      'fullPayPrincipal',
      'fullPayInterest',
      'fullPayPenalty',
      'principalCovered',
      'interestCalculate',
      'payment_status',
      'payment_due_status',
      'payment_done_date',
      'loanId',
      'paid_principal',
      'paid_interest',
      'penalty_days',
    ];

    const emiOptions: any = {
      where: {
        emi_date: {
          [Op.gte]: minDate,
          [Op.lte]: maxDate,
        },
      },
      order: [['id', 'desc']],
    };

    const emiListData: any = await this.repository?.getTableWhereData?.(
      emiAttributes,
      emiOptions,
    );
    if (emiListData == k500Error)
      throw new Error('Failed to retrieve EMI data');

    let loanIds = emiListData?.map?.((el: any) => el?.loanId) ?? [];
    loanIds = [...new Set(loanIds)];

    const attributes = [
      'id',
      'paidAmount',
      'type',
      'penaltyAmount',
      'principalAmount',
      'interestAmount',
      'emiId',
      'loanId',
    ];
    const options = {
      where: {
        loanId: loanIds,
        status: kCompleted,
      },
    };
    const transList = await this.transRepo.getTableWhereData(
      attributes,
      options,
    );

    if (transList == k500Error) throw new Error();

    const loanTransactionMap = new Map();

    transList?.forEach?.((data: any) => {
      if (data?.loanId) {
        if (!loanTransactionMap.has(data.loanId)) {
          loanTransactionMap.set(data.loanId, [data]);
        } else {
          loanTransactionMap.get(data.loanId)?.push?.(data);
        }
      }
    });

    const finalData: any = {
      totalAmount: 0,
      prePaidAmount: 0,
      prePaidCount: 0,
    };
    this.createBifurcationColumns(finalData);

    const remainingAmount =
      await this.reportAnalysisService.getTodaysRemainingAutodebits();

    emiListData.forEach((el) => {
      const transactionData = loanTransactionMap.get(el?.loanId);

      el.loan = {
        ...(el?.loan ?? {}),
        transactionData: transactionData ? [...transactionData] : [],
      };

      const data = this.newGetRateEMI(el);

      let strKey = this.getBifurcationDays(data.delayDays);
      if (data.paymentStatus == 'POST_PAID' && el.payment_due_status == 0)
        strKey = 'D1';

      let strKeyCount = `${strKey}_count`;
      const strKeyAmount = `${strKey}_amount`;

      finalData.totalAmount += data.totalExpected ?? 0;

      if (data.paymentStatus === 'PRE_PAID') {
        finalData.prePaidCount++;
        finalData.prePaidAmount +=
          (data?.paidPrincipal ?? 0) + (data?.paidInterest ?? 0);
      } else if (data.paymentStatus === 'ON_TIME') {
        finalData[strKeyCount] = (finalData[strKeyCount] ?? 0) + 1;
        finalData[strKeyAmount] =
          (finalData[strKeyAmount] ?? 0) +
          (data?.paidPrincipal ?? 0) +
          (data?.paidInterest ?? 0);
        finalData.onTimeCount = (finalData.onTimeCount ?? 0) + 1;
        finalData.onTimeAmount =
          (finalData.onTimeAmount ?? 0) +
          (data?.paidPrincipal ?? 0) +
          (data?.paidInterest ?? 0);
      } else if (
        data.paymentStatus === 'POST_PAID' ||
        data.paymentStatus === 'PARTIAL_PAID' ||
        (data.paymentStatus === 'UN_PAID' && data?.totalPaid > 0)
      ) {
        finalData[strKeyCount] = (finalData[strKeyCount] ?? 0) + 1;
        finalData[strKeyAmount] =
          (finalData[strKeyAmount] ?? 0) +
          (data?.paidPrincipal ?? 0) +
          (data?.paidInterest ?? 0);
      }
    });

    finalData.totalAmount = Math.floor(
      (finalData.totalAmount ?? 0) - remainingAmount,
    );

    const finalizedData = {
      preEMIData: {
        ratio: parseFloat(
          (
            ((finalData.prePaidAmount ?? 0) / (finalData.totalAmount ?? 1)) *
            100
          ).toFixed(2),
        ),
        amount: Math.floor(finalData.prePaidAmount ?? 0),
        count: finalData.prePaidCount ?? 0,
      },
    };

    for (const key in finalData) {
      if (['totalAmount', 'prePaidCount', 'prePaidAmount'].includes(key))
        continue;

      const prepareKey = key.split('_')[0];
      if (!finalizedData[prepareKey]) {
        finalizedData[prepareKey] = {
          ratio: parseFloat(
            (
              ((finalData[`${prepareKey}_amount`] ?? 0) /
                (finalData.totalAmount ?? 1)) *
              100
            ).toFixed(2),
          ),
          amount: Math.floor(finalData[`${prepareKey}_amount`] ?? 0),
          count: finalData[`${prepareKey}_count`] ?? 0,
        };
      }
    }

    return finalizedData;
  }

  //#endregion

  newGetRateEMI(emi) {
    const data = {
      dueDate: null,
      expectedPrincipal: 0,
      expectedInterest: 0,
      totalExpected: 0,
      paidPrincipal: 0,
      paidInterest: 0,
      totalPaid: 0,
      prePaidPrinciple: 0,
      prePaidInterest: 0,
      totalPrePaidAmount: 0,
      paymentStatus: '-',
      diffInterest: 0,
      delayDays: 0,
    };

    try {
      data.dueDate = this.typeService.getGlobalDate(new Date(emi.emi_date));
      data.delayDays = emi?.penalty_days ?? 0;
      const loan = emi.loan;
      const transaction = loan.transactionData;
      const emiDate = this.typeService.getGlobalDate(emi.emi_date);
      const status = emi?.payment_status ?? '0';

      data.expectedPrincipal = emi.principalCovered;
      data.expectedInterest = emi.interestCalculate;

      const paymentData: any = this.calculatePartPayment(emi.id, transaction);

      data.paidPrincipal = paymentData?.paidPrincipal ?? 0;
      data.paidInterest = paymentData?.paidInterest ?? 0;
      const isEmiPaid = paymentData?.isEmiPaid ?? false;

      if (status === '1') {
        let isPrePay = false;
        const emiDoneDate = emi?.payment_done_date;
        if (emiDoneDate) {
          const payDate = this.typeService.getGlobalDate(emiDoneDate);
          isPrePay = payDate.getTime() < emiDate.getTime();
          data.paymentStatus = isPrePay
            ? 'PRE_PAID'
            : payDate.getTime() === emiDate.getTime()
            ? 'ON_TIME'
            : 'POST_PAID';
        }

        if (isPrePay) {
          if (!isEmiPaid) {
            data.paidPrincipal += emi?.fullPayPrincipal ?? 0;
            data.paidInterest += emi?.fullPayInterest ?? 0;
          }
          data.prePaidPrinciple = data.paidPrincipal;
          data.prePaidInterest = data.paidInterest;
          data.expectedInterest = data.paidInterest;
        } else if (!isEmiPaid) {
          data.paidPrincipal += emi?.fullPayPrincipal ?? 0;
          data.paidInterest += emi?.fullPayInterest ?? 0;
        }
      } else {
        data.paymentStatus = paymentData.isPartPaid
          ? 'PARTIAL_PAID'
          : 'UN_PAID';
      }

      const eTotal = data.expectedPrincipal + data.expectedInterest;
      const pTotal = data.paidPrincipal + data.paidInterest;

      const diff = eTotal - pTotal;
      if ((diff > 10 || diff < -10) && status == '1') {
        this.getDiff(data);
        if (data.diffInterest < 0) data.paidInterest = data.expectedInterest;

        this.getDiff(data);
      }

      data.totalPaid = data.paidPrincipal + data.paidInterest;
      data.totalExpected = data.expectedPrincipal + data.expectedInterest;
      data.totalPrePaidAmount = data.prePaidPrinciple + data.prePaidInterest;
    } catch (error) {}

    return data;
  }

  private calculatePartPayment(emiId, transactionData) {
    const data = {
      paidPenalty: 0,
      paidPrincipal: 0,
      isEmiPaid: false,
      paidInterest: 0,
      paidPenalCharge: 0,
      paidLegalCharge: 0,
      paidBounceCharge: 0,
      refundAmount: 0,
    };
    try {
      let tran = [...transactionData];
      tran.forEach((ele) => {
        if (emiId == ele.emiId && ele.type == 'REFUND')
          data.refundAmount += ele.paidAmount;
      });
      tran = tran.sort(
        (a, b) =>
          a.penaltyAmount +
          a.principalAmount +
          a.interestAmount -
          (b.penaltyAmount + b.principalAmount + b.interestAmount),
      );
      tran.forEach((ele) => {
        if (
          emiId == ele.emiId &&
          (ele.type == 'PARTPAY' || ele.type == 'EMIPAY')
        ) {
          //SUBTRACK THE REFUND FROM PAID AMOUNT
          if (ele.paidAmount + data.refundAmount == 0 && ele.type == 'EMIPAY')
            data.refundAmount = 0;
          else {
            if (ele.type == 'EMIPAY') data.isEmiPaid = true;
            data.paidPenalty += ele?.penaltyAmount ?? 0;
            data.paidPrincipal += ele?.principalAmount ?? 0;
            data.paidInterest += ele?.interestAmount ?? 0;
            data.paidPenalCharge += ele?.penalCharge ?? 0;
            data.paidLegalCharge += ele?.legalCharge ?? 0;
            data.paidBounceCharge += ele?.bounceCharge ?? 0;
          }
        }
      });
      if (tran.length > 0) {
        const find = tran.find((f) => f.type == 'FULLPAY');
        if (!find) data.isEmiPaid = true;
      }
      // if (data.refundAmount != 0) console.log(emiId, data.refundAmount, data);
    } catch (error) {}
    return data;
  }

  private getDiff(data) {
    data.diffPrincipal = data.expectedPrincipal - data.paidPrincipal;
    if (data.diffPrincipal < 0) {
      const amount = -1 * data.diffPrincipal;
      data.paidPrincipal -= amount;
      data.paidInterest += amount;
      data.diffPrincipal = 0;
    }
    data.diffInterest = data.expectedInterest - data.paidInterest;
    if (data.diffInterest < 0) {
      const amount = -1 * data.diffInterest;
      data.paidInterest -= amount;
      data.paidPenalty += amount;
      data.diffInterest = 0;
    }
    data.diffPenalty = data.expectedPenalty - data.paidPenalty;
    if (data.diffPenalty < 0) {
      data.diffPenalty = 0;
      data.expectedPenalty += data.paidPenalty - data.expectedPenalty;
    }
    return data;
  }
}
